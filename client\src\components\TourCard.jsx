
import { Link } from "react-router-dom";
import useFetch from "../hooks/useFetch";

const TourCard = ( {tour }) => {
  const {_id, title,city, photo, price, featured,reviews } = tour;
  const {data:tourReviews}=useFetch(`http://localhost:9000/reviews/${_id}`);
  //const totalRating = reviews.reduce((acc,item)=>acc+item.rating,0);
  //const avgRating=totalRating===0?"":totalRating===1?totalRating:totalRating/reviews?.length;
  return (
    <div className="w-full max-w-sm bg-white rounded-xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 overflow-hidden">
      {/* Image Section */}
      <div className="relative h-48 sm:h-56 overflow-hidden">
        <img
          src={photo}
          className="w-full h-full object-cover transition-transform duration-300 hover:scale-110"
          alt={title || "Tour destination"}
        />
        {featured && (
          <span className="absolute top-3 right-3 bg-yellow-400 text-gray-800 px-2 py-1 rounded-full text-xs font-semibold shadow-md">
            Featured
          </span>
        )}
        <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
      </div>

      {/* Content Section */}
      <div className="p-4 sm:p-6">
        {/* Title */}
        <div className="mb-3">
          <h5 className="font-bold text-lg sm:text-xl text-gray-800 hover:text-pink-600 transition-colors duration-200 line-clamp-2">
            <Link to={`/tours/${_id}`} className="hover:underline">
              {title}
            </Link>
          </h5>
        </div>

        {/* Location and Rating */}
        <div className="flex items-center justify-between mb-4 text-sm">
          <div className="flex items-center text-gray-600">
            <i className="ri-map-pin-line mr-1 text-pink-500"></i>
            <span className="truncate">{city}</span>
          </div>
          <div className="flex items-center bg-gray-100 px-2 py-1 rounded-full">
            <i className="ri-star-line mr-1 text-yellow-500"></i>
            <span className="text-gray-700 text-xs">
              {tourReviews.length === 0 ? "Not Rated" : `(${tourReviews.length})`}
            </span>
          </div>
        </div>

        {/* Price and View Details Button */}
        <div className="flex items-center justify-between">
          <div className="text-gray-800">
            <span className="text-xl sm:text-2xl font-bold text-pink-600">${price}</span>
            <span className="text-sm text-gray-500 ml-1">/person</span>
          </div>
          <Link
            to={`/tours/${_id}`}
            className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white font-semibold py-2 px-4 sm:px-6 rounded-full text-sm transition-all duration-200 transform hover:scale-105 shadow-md"
          >
            View Details
          </Link>
        </div>
      </div>
    </div>
  );
};

export default TourCard;
