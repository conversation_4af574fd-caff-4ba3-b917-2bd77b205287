const mongoose=require('mongoose');

const tourSchema = new mongoose.Schema(
  {
    title: {
      type: String,
      required: true,
      unique: true,
    },
    city: {
      type: String,
      required: true,
    },
    address: {
      type: String,
      required: true,
    },
    distance: {
      type: Number,
      required: true,
    },
    photo: {
      type: String,
      required: true,
    },
    desc: {
      type: String,
      required: true,
    },
    price: {
      type: Number,
      required: true,
    },
    maxGroupSize: {
      type: Number,
      required: true,
    },
    // Enhanced fields for AI Travel Planner
    duration: {
      type: Number, // Duration in days
      default: 1,
    },
    difficulty: {
      type: String,
      enum: ['Easy', 'Moderate', 'Challenging', 'Expert'],
      default: 'Easy',
    },
    activities: [{
      type: String, // e.g., 'Adventure', 'Cultural', 'Relaxation', 'Wildlife', 'Photography'
    }],
    bestTimeToVisit: [{
      type: String, // e.g., 'Spring', 'Summer', 'Autumn', 'Winter'
    }],
    coordinates: {
      latitude: {
        type: Number,
        required: false,
      },
      longitude: {
        type: Number,
        required: false,
      }
    },
    amenities: [{
      type: String, // e.g., 'WiFi', 'Parking', 'Restaurant', 'Guide'
    }],
    travelStyle: [{
      type: String, // e.g., 'Budget', 'Luxury', 'Adventure', 'Family', 'Solo', 'Couple'
    }],

    reviews: [
      {
        type: mongoose.Types.ObjectId,
        ref: "review",
      },
    ],

    featured: {
      type: Boolean,
      default: false,
    },
  },
  { timestamps: true }
);

module.exports=mongoose.model("tour", tourSchema);