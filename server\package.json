{"dependencies": {"axios": "^1.11.0", "bcryptjs": "^2.4.3", "body-parser": "^1.20.2", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "dotenv": "^16.0.3", "express": "^4.18.2", "jsonwebtoken": "^9.0.0", "mongodb": "^5.0.1", "mongoose": "^6.9.1", "nodemailer": "^6.9.3", "nodemon": "^1.14.9", "openai": "^5.11.0"}, "name": "server", "version": "1.0.0", "main": "index.js", "scripts": {"dev": "nodemon index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": ""}