const express = require('express');
const router = express.Router();
const {
  createTravelPlan,
  getUserTravelPlans,
  getTravelPlan,
  updateTravelPlan,
  deleteTravelPlan
} = require('../controllers/simpleTravelPlannerController');

// Create new travel plan
router.post('/create', createTravelPlan);

// Get user's travel plans
router.get('/user/:userId', getUserTravelPlans);

// Get single travel plan
router.get('/:planId', getTravelPlan);

// Update travel plan
router.put('/:planId', updateTravelPlan);

// Delete travel plan
router.delete('/:planId', deleteTravelPlan);

module.exports = router;
