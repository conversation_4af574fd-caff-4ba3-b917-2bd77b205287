const mongoose = require('mongoose');

const simpleTravelPlanSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  planName: {
    type: String,
    required: true,
    trim: true
  },
  destination: {
    type: String,
    required: true
  },
  startDate: {
    type: Date,
    required: true
  },
  endDate: {
    type: Date,
    required: true
  },
  budget: {
    type: Number,
    required: true,
    min: 0
  },
  groupSize: {
    type: Number,
    required: true,
    min: 1,
    default: 1
  },
  travelStyle: {
    type: String,
    enum: ['Budget', 'Mid-range', 'Luxury', 'Adventure', 'Family'],
    required: true
  },
  activities: [{
    type: String,
    enum: ['Adventure', 'Cultural', 'Food', 'Nature', 'Photography', 'Shopping', 'Relaxation']
  }],
  recommendations: [{
    title: String,
    description: String,
    type: String,
    estimatedCost: Number
  }],
  status: {
    type: String,
    enum: ['Planning', 'Confirmed', 'Completed'],
    default: 'Planning'
  }
}, {
  timestamps: true
});

module.exports = mongoose.model('SimpleTravelPlan', simpleTravelPlanSchema);
