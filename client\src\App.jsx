import React from 'react'
import {<PERSON>rowser<PERSON>outer,Routes,Route} from 'react-router-dom';
import Header from './components/Header';
import Footer from './components/Footer';
import Home from './pages/Home';
import Login from './pages/Login';
import Register from './pages/Register';




import AIRecommendationsDemo from './components/AIRecommendationsDemo';
import FullyWorkingAIPlanner from './components/FullyWorkingAIPlanner';
import WonderPlanStyleAI from './components/WonderPlanStyleAI';
import AISearchPlanner from './components/AISearchPlanner';
import AITravelPlanDetails from './components/AITravelPlanDetails';
import AITravelPlansList from './components/AITravelPlansList';
import Profile from './components/Profile';
import { Navigate } from 'react-router-dom';
import 'remixicon/fonts/remixicon.css'


function App() {

  return (
    <div className="min-h-screen gradient-bg font-inter">
      <BrowserRouter>
      <Header/>
        <Routes>
          <Route path="/" element={<Navigate to="/home"/>}/>
          <Route path="/home" element={<Home/>}/>
          <Route path="/login" element={<Login/>}/>
          <Route path="/register" element={<Register/>}/>
          <Route path="/ai-travel-planner" element={<AISearchPlanner/>}/>
          <Route path="/ai-travel-plan/:planId" element={<AITravelPlanDetails/>}/>
          <Route path="/ai-travel-plans" element={<AITravelPlansList/>}/>
          <Route path="/ai-demo" element={<AIRecommendationsDemo/>}/>
          <Route path="/profile" element={<Profile/>}/>
        </Routes>
        <Footer/>
      </BrowserRouter>
    </div>
  )
}

export default App
