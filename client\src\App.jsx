import React from 'react'
import {BrowserRouter,Routes,Route} from 'react-router-dom';
import Header from './components/Header';
import Footer from './components/Footer';
import Home from './pages/Home';
import Login from './pages/Login';
import Register from './pages/Register';
import TourDetails from './pages/TourDetails';
import SearchResult from './components/SearchResult';
import ThankYou from './components/ThankYou';
import TravelPlanner from './components/TravelPlanner';
import TravelPlanDetails from './components/TravelPlanDetails';
import TravelPlansList from './components/TravelPlansList';
import SimpleTravelPlanner from './components/SimpleTravelPlanner';

import AIRecommendationsDemo from './components/AIRecommendationsDemo';
import FullyWorkingAIPlanner from './components/FullyWorkingAIPlanner';
import AITravelPlanDetails from './components/AITravelPlanDetails';
import AITravelPlansList from './components/AITravelPlansList';
import Profile from './components/Profile';
import { Navigate } from 'react-router-dom';
import 'remixicon/fonts/remixicon.css'


function App() {

  return (
    <div className="min-h-screen gradient-bg font-inter">
      <BrowserRouter>
      <Header/>
        <Routes>
          <Route path="/" element={<Navigate to="/home"/>}/>
          <Route path="/home" element={<Home/>}/>
          <Route path="/tours/:id" element={<TourDetails/>}/>
          <Route path="/thankyou" element={<ThankYou/>}/>
          <Route path="/login" element={<Login/>}/>
          <Route path="/register" element={<Register/>}/>
          <Route path="/search" element={<SearchResult/>}/>
          <Route path="/travel-planner" element={<TravelPlanner/>}/>
          <Route path="/travel-plan/:planId" element={<TravelPlanDetails/>}/>
          <Route path="/my-travel-plans" element={<TravelPlansList/>}/>
          <Route path="/simple-travel-planner" element={<SimpleTravelPlanner/>}/>

          <Route path="/ai-travel-planner" element={<FullyWorkingAIPlanner/>}/>
          <Route path="/ai-travel-plan/:planId" element={<AITravelPlanDetails/>}/>
          <Route path="/ai-travel-plans" element={<AITravelPlansList/>}/>
          <Route path="/ai-demo" element={<AIRecommendationsDemo/>}/>
          <Route path="/profile" element={<Profile/>}/>
        </Routes>
        <Footer/>
      </BrowserRouter>
    </div>
  )
}

export default App
