import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import axios from 'axios';
import useFetch from '../hooks/useFetch';

const TravelPlanDetails = () => {
  const { planId } = useParams();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  
  const { data: travelPlan, loading: planLoading } = useFetch(
    `https://traveltrek.onrender.com/travel-planner/${planId}`
  );

  const [selectedTab, setSelectedTab] = useState('overview');

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const optimizeRoute = async () => {
    setLoading(true);
    try {
      await axios.post(`https://traveltrek.onrender.com/travel-planner/${planId}/optimize`);
      window.location.reload(); // Refresh to show optimized route
    } catch (error) {
      setError('Error optimizing route');
    } finally {
      setLoading(false);
    }
  };

  const getRecommendationIcon = (type) => {
    switch (type) {
      case 'tour': return 'ri-map-pin-line';
      case 'restaurant': return 'ri-restaurant-line';
      case 'activity': return 'ri-gamepad-line';
      case 'accommodation': return 'ri-hotel-line';
      case 'transport': return 'ri-car-line';
      default: return 'ri-star-line';
    }
  };

  if (planLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="text-center">
          <i className="ri-loader-4-line text-4xl text-blue-600 animate-spin mb-4"></i>
          <p className="text-gray-600">Loading your travel plan...</p>
        </div>
      </div>
    );
  }

  if (!travelPlan) {
    return (
      <div className="text-center py-12">
        <i className="ri-error-warning-line text-6xl text-red-500 mb-4"></i>
        <h2 className="text-2xl font-bold text-gray-800 mb-2">Travel Plan Not Found</h2>
        <p className="text-gray-600 mb-4">The travel plan you're looking for doesn't exist.</p>
        <button
          onClick={() => navigate('/travel-planner')}
          className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md"
        >
          Create New Plan
        </button>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto p-6 font-shantell">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg p-6 mb-6">
        <div className="flex justify-between items-start">
          <div>
            <h1 className="text-3xl font-bold mb-2">{travelPlan.planName}</h1>
            <p className="text-blue-100 mb-4">
              {travelPlan.destinations?.map(dest => dest.city).join(' → ')}
            </p>
            <div className="flex flex-wrap gap-4 text-sm">
              <span className="bg-white/20 px-3 py-1 rounded-full">
                <i className="ri-calendar-line mr-1"></i>
                {formatDate(travelPlan.destinations?.[0]?.arrivalDate)} - 
                {formatDate(travelPlan.destinations?.[travelPlan.destinations.length - 1]?.departureDate)}
              </span>
              <span className="bg-white/20 px-3 py-1 rounded-full">
                <i className="ri-money-dollar-circle-line mr-1"></i>
                {formatCurrency(travelPlan.totalBudget)}
              </span>
              <span className="bg-white/20 px-3 py-1 rounded-full">
                <i className="ri-user-line mr-1"></i>
                {travelPlan.preferences?.groupSize} {travelPlan.preferences?.groupSize === 1 ? 'Person' : 'People'}
              </span>
            </div>
          </div>
          <div className="text-right">
            <span className={`px-3 py-1 rounded-full text-sm ${
              travelPlan.status === 'Planning' ? 'bg-yellow-500' :
              travelPlan.status === 'Confirmed' ? 'bg-green-500' :
              travelPlan.status === 'In Progress' ? 'bg-blue-500' :
              'bg-gray-500'
            }`}>
              {travelPlan.status}
            </span>
          </div>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="border-b border-gray-200 mb-6">
        <nav className="flex space-x-8">
          {['overview', 'itinerary', 'recommendations', 'budget'].map(tab => (
            <button
              key={tab}
              onClick={() => setSelectedTab(tab)}
              className={`py-2 px-1 border-b-2 font-medium text-sm capitalize ${
                selectedTab === tab
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              {tab}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      {selectedTab === 'overview' && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Destinations */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-xl font-bold mb-4 flex items-center">
              <i className="ri-map-pin-line text-blue-600 mr-2"></i>
              Destinations
            </h3>
            <div className="space-y-4">
              {travelPlan.destinations?.map((dest, index) => (
                <div key={index} className="border-l-4 border-blue-500 pl-4">
                  <h4 className="font-semibold text-lg">{dest.city}, {dest.country}</h4>
                  <p className="text-gray-600 text-sm">
                    {formatDate(dest.arrivalDate)} - {formatDate(dest.departureDate)}
                  </p>
                </div>
              ))}
            </div>
          </div>

          {/* Preferences */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-xl font-bold mb-4 flex items-center">
              <i className="ri-settings-line text-blue-600 mr-2"></i>
              Travel Preferences
            </h3>
            <div className="space-y-3">
              <div>
                <span className="font-medium">Style:</span>
                <span className="ml-2 text-gray-600">{travelPlan.travelStyle}</span>
              </div>
              <div>
                <span className="font-medium">Accommodation:</span>
                <span className="ml-2 text-gray-600">{travelPlan.preferences?.accommodation}</span>
              </div>
              <div>
                <span className="font-medium">Transportation:</span>
                <span className="ml-2 text-gray-600">{travelPlan.preferences?.transportation}</span>
              </div>
              <div>
                <span className="font-medium">Activities:</span>
                <div className="mt-2 flex flex-wrap gap-2">
                  {travelPlan.preferences?.activities?.map((activity, index) => (
                    <span key={index} className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs">
                      {activity}
                    </span>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {selectedTab === 'itinerary' && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex justify-between items-center mb-6">
            <h3 className="text-xl font-bold flex items-center">
              <i className="ri-calendar-check-line text-blue-600 mr-2"></i>
              Itinerary
            </h3>
            <button
              onClick={optimizeRoute}
              disabled={loading}
              className="bg-green-600 hover:bg-green-700 disabled:bg-green-400 text-white px-4 py-2 rounded-md text-sm flex items-center"
            >
              {loading ? (
                <i className="ri-loader-4-line animate-spin mr-2"></i>
              ) : (
                <i className="ri-route-line mr-2"></i>
              )}
              Optimize Route
            </button>
          </div>
          
          {travelPlan.itinerary?.length > 0 ? (
            <div className="space-y-4">
              {travelPlan.itinerary.map((item, index) => (
                <div key={index} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex justify-between items-start">
                    <div>
                      <h4 className="font-semibold">Day {item.day}</h4>
                      <p className="text-gray-600">{item.tourId?.title || 'Tour'}</p>
                      <p className="text-sm text-gray-500">
                        {item.startTime} - {item.endTime}
                      </p>
                      {item.notes && (
                        <p className="text-sm text-gray-600 mt-2">{item.notes}</p>
                      )}
                    </div>
                    <span className="text-green-600 font-semibold">
                      {formatCurrency(item.estimatedCost)}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <i className="ri-calendar-line text-4xl text-gray-400 mb-4"></i>
              <p className="text-gray-600">No itinerary items yet. Start adding tours to your plan!</p>
            </div>
          )}
        </div>
      )}

      {selectedTab === 'recommendations' && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-xl font-bold mb-6 flex items-center">
            <i className="ri-lightbulb-line text-blue-600 mr-2"></i>
            AI Recommendations
          </h3>
          
          {travelPlan.aiRecommendations?.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {travelPlan.aiRecommendations.map((rec, index) => (
                <div key={index} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center">
                      <i className={`${getRecommendationIcon(rec.type)} text-blue-600 mr-2`}></i>
                      <h4 className="font-semibold">{rec.title}</h4>
                    </div>
                    <span className="text-green-600 font-semibold text-sm">
                      {formatCurrency(rec.estimatedCost)}
                    </span>
                  </div>
                  <p className="text-gray-600 text-sm mb-2">{rec.description}</p>
                  <p className="text-xs text-gray-500 mb-2">
                    <i className="ri-map-pin-line mr-1"></i>
                    {rec.location}
                  </p>
                  <div className="flex justify-between items-center">
                    <div className="flex items-center">
                      <div className="flex text-yellow-400">
                        {[...Array(5)].map((_, i) => (
                          <i key={i} className={`ri-star-${i < rec.rating ? 'fill' : 'line'} text-xs`}></i>
                        ))}
                      </div>
                      <span className="text-xs text-gray-500 ml-1">({rec.rating})</span>
                    </div>
                    <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs capitalize">
                      {rec.type}
                    </span>
                  </div>
                  <p className="text-xs text-gray-600 mt-2 italic">
                    {rec.reasonForRecommendation}
                  </p>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <i className="ri-lightbulb-line text-4xl text-gray-400 mb-4"></i>
              <p className="text-gray-600">AI recommendations will appear here based on your preferences.</p>
            </div>
          )}
        </div>
      )}

      {selectedTab === 'budget' && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-xl font-bold mb-6 flex items-center">
            <i className="ri-money-dollar-circle-line text-blue-600 mr-2"></i>
            Budget Overview
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <h4 className="text-lg font-semibold text-blue-800">Total Budget</h4>
              <p className="text-2xl font-bold text-blue-600">
                {formatCurrency(travelPlan.totalBudget)}
              </p>
            </div>
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <h4 className="text-lg font-semibold text-green-800">Spent</h4>
              <p className="text-2xl font-bold text-green-600">
                {formatCurrency(travelPlan.currentSpent || 0)}
              </p>
            </div>
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <h4 className="text-lg font-semibold text-gray-800">Remaining</h4>
              <p className="text-2xl font-bold text-gray-600">
                {formatCurrency(travelPlan.totalBudget - (travelPlan.currentSpent || 0))}
              </p>
            </div>
          </div>

          {/* Budget Progress Bar */}
          <div className="mb-6">
            <div className="flex justify-between text-sm text-gray-600 mb-2">
              <span>Budget Usage</span>
              <span>
                {Math.round(((travelPlan.currentSpent || 0) / travelPlan.totalBudget) * 100)}%
              </span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{
                  width: `${Math.min(((travelPlan.currentSpent || 0) / travelPlan.totalBudget) * 100, 100)}%`
                }}
              ></div>
            </div>
          </div>

          {/* Expense Breakdown */}
          {travelPlan.itinerary?.length > 0 && (
            <div>
              <h4 className="font-semibold mb-4">Expense Breakdown</h4>
              <div className="space-y-2">
                {travelPlan.itinerary.map((item, index) => (
                  <div key={index} className="flex justify-between items-center py-2 border-b border-gray-100">
                    <span className="text-gray-700">{item.tourId?.title || `Day ${item.day} Activity`}</span>
                    <span className="font-semibold">{formatCurrency(item.estimatedCost)}</span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4 mt-6">
          <p className="text-red-600 text-sm">{error}</p>
        </div>
      )}
    </div>
  );
};

export default TravelPlanDetails;
