const TravelPlan = require('../models/travelPlannerModel');
const Tour = require('../models/toursModel');
const { generatePersonalizedRecommendations, generateOptimalRoute } = require('../utils/aiRecommendationEngine');

// Enhanced AI-powered recommendation engine
const generateAIRecommendations = async (preferences, destinations, budget, travelStyle) => {
  try {
    const recommendations = [];

    // Get tours based on preferences and destinations
    for (const destination of destinations) {
      const tours = await Tour.find({
        city: new RegExp(destination.city, 'i'),
        price: { $lte: budget * 0.3 }, // Max 30% of budget per tour
        activities: { $in: preferences.activities || [] }
      }).limit(3);

      tours.forEach(tour => {
        recommendations.push({
          type: 'tour',
          title: tour.title,
          description: tour.desc,
          location: tour.city,
          estimatedCost: tour.price,
          rating: 4.5, // Default rating, can be calculated from reviews
          reasonForRecommendation: `Perfect match for your ${preferences.activities?.join(', ')} preferences in ${destination.city}`
        });
      });
    }

    // Use the AI recommendation engine for additional personalized recommendations
    const aiRecommendations = generatePersonalizedRecommendations(preferences, destinations, budget, travelStyle);
    recommendations.push(...aiRecommendations);

    return recommendations.slice(0, 12); // Return top 12 recommendations
  } catch (error) {
    console.error('Error generating AI recommendations:', error);
    return [];
  }
};

const getStyleBasedRecommendations = (travelStyle, destinations) => {
  const recommendations = [];
  
  destinations.forEach(destination => {
    switch (travelStyle) {
      case 'Budget':
        recommendations.push({
          type: 'accommodation',
          title: 'Budget Hostel',
          description: 'Clean and affordable accommodation with shared facilities',
          location: destination.city,
          estimatedCost: 25,
          rating: 4.0,
          reasonForRecommendation: 'Great value for money accommodation option'
        });
        break;
      case 'Luxury':
        recommendations.push({
          type: 'accommodation',
          title: '5-Star Resort',
          description: 'Luxury accommodation with premium amenities',
          location: destination.city,
          estimatedCost: 300,
          rating: 4.8,
          reasonForRecommendation: 'Premium experience matching your luxury travel style'
        });
        break;
      case 'Family':
        recommendations.push({
          type: 'activity',
          title: 'Family-Friendly Activities',
          description: 'Kid-friendly attractions and activities',
          location: destination.city,
          estimatedCost: 50,
          rating: 4.5,
          reasonForRecommendation: 'Perfect for family travel with children'
        });
        break;
    }
  });
  
  return recommendations;
};

// Create a new travel plan
const createTravelPlan = async (req, res) => {
  try {
    const { userId, planName, destinations, totalBudget, travelStyle, preferences } = req.body;

    // Generate AI recommendations
    const aiRecommendations = await generateAIRecommendations(preferences, destinations, totalBudget, travelStyle);

    const newTravelPlan = new TravelPlan({
      userId,
      planName,
      destinations,
      totalBudget,
      travelStyle,
      preferences,
      aiRecommendations,
      itinerary: [],
      status: 'Planning'
    });

    const savedPlan = await newTravelPlan.save();
    
    res.status(201).json({
      success: true,
      message: 'Travel plan created successfully',
      data: savedPlan
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error creating travel plan',
      error: error.message
    });
  }
};

// Get user's travel plans
const getUserTravelPlans = async (req, res) => {
  try {
    const { userId } = req.params;
    
    const travelPlans = await TravelPlan.find({ userId })
      .populate('itinerary.tourId')
      .sort({ createdAt: -1 });

    res.status(200).json({
      success: true,
      message: 'Travel plans retrieved successfully',
      data: travelPlans
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error retrieving travel plans',
      error: error.message
    });
  }
};

// Get single travel plan
const getTravelPlan = async (req, res) => {
  try {
    const { planId } = req.params;
    
    const travelPlan = await TravelPlan.findById(planId)
      .populate('itinerary.tourId')
      .populate('userId', 'username email');

    if (!travelPlan) {
      return res.status(404).json({
        success: false,
        message: 'Travel plan not found'
      });
    }

    res.status(200).json({
      success: true,
      message: 'Travel plan retrieved successfully',
      data: travelPlan
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error retrieving travel plan',
      error: error.message
    });
  }
};

// Update travel plan
const updateTravelPlan = async (req, res) => {
  try {
    const { planId } = req.params;
    const updateData = req.body;

    const updatedPlan = await TravelPlan.findByIdAndUpdate(
      planId,
      updateData,
      { new: true, runValidators: true }
    ).populate('itinerary.tourId');

    if (!updatedPlan) {
      return res.status(404).json({
        success: false,
        message: 'Travel plan not found'
      });
    }

    res.status(200).json({
      success: true,
      message: 'Travel plan updated successfully',
      data: updatedPlan
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error updating travel plan',
      error: error.message
    });
  }
};

// Add tour to itinerary
const addToItinerary = async (req, res) => {
  try {
    const { planId } = req.params;
    const { day, tourId, startTime, endTime, notes } = req.body;

    const tour = await Tour.findById(tourId);
    if (!tour) {
      return res.status(404).json({
        success: false,
        message: 'Tour not found'
      });
    }

    const travelPlan = await TravelPlan.findById(planId);
    if (!travelPlan) {
      return res.status(404).json({
        success: false,
        message: 'Travel plan not found'
      });
    }

    const itineraryItem = {
      day,
      tourId,
      startTime,
      endTime,
      notes: notes || '',
      estimatedCost: tour.price
    };

    travelPlan.itinerary.push(itineraryItem);
    travelPlan.currentSpent += tour.price;

    await travelPlan.save();

    res.status(200).json({
      success: true,
      message: 'Tour added to itinerary successfully',
      data: travelPlan
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error adding tour to itinerary',
      error: error.message
    });
  }
};

// Generate optimized route
const generateOptimizedRoute = async (req, res) => {
  try {
    const { planId } = req.params;
    
    const travelPlan = await TravelPlan.findById(planId).populate('itinerary.tourId');
    if (!travelPlan) {
      return res.status(404).json({
        success: false,
        message: 'Travel plan not found'
      });
    }

    // Use AI recommendation engine for route optimization
    const optimizedItinerary = generateOptimalRoute(travelPlan.itinerary);

    travelPlan.itinerary = optimizedItinerary;
    travelPlan.lastOptimized = new Date();
    await travelPlan.save();

    res.status(200).json({
      success: true,
      message: 'Route optimized successfully using AI',
      data: travelPlan
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error optimizing route',
      error: error.message
    });
  }
};

module.exports = {
  createTravelPlan,
  getUserTravelPlans,
  getTravelPlan,
  updateTravelPlan,
  addToItinerary,
  generateOptimizedRoute
};
