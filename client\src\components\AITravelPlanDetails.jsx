import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import useFetch from '../hooks/useFetch';

const AITravelPlanDetails = () => {
  const { planId } = useParams();
  const navigate = useNavigate();
  
  const { data: travelPlan, loading } = useFetch(
    `http://localhost:9000/ai-travel-planner/${planId}`
  );

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const calculateDuration = (startDate, endDate) => {
    return Math.ceil((new Date(endDate) - new Date(startDate)) / (1000 * 60 * 60 * 24));
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="text-center">
          <i className="ri-loader-4-line text-4xl text-blue-600 animate-spin mb-4"></i>
          <p className="text-gray-600 text-lg">Loading your AI travel plan...</p>
        </div>
      </div>
    );
  }

  if (!travelPlan) {
    return (
      <div className="max-w-md mx-auto mt-20 p-6 bg-white rounded-lg shadow-md text-center">
        <i className="ri-error-warning-line text-6xl text-red-500 mb-4"></i>
        <h2 className="text-2xl font-bold text-gray-800 mb-2">Travel Plan Not Found</h2>
        <p className="text-gray-600 mb-4">The travel plan you're looking for doesn't exist or may have been deleted.</p>
        <div className="flex gap-4 justify-center">
          <button
            onClick={() => navigate('/ai-travel-planner')}
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md"
          >
            Create New Plan
          </button>
          <button
            onClick={() => navigate('/ai-travel-plans')}
            className="bg-gray-600 hover:bg-gray-700 text-white px-6 py-2 rounded-md"
          >
            View All Plans
          </button>
        </div>
      </div>
    );
  }

  const duration = calculateDuration(travelPlan.startDate, travelPlan.endDate);
  const totalRecommendationCost = travelPlan.recommendations?.reduce((sum, rec) => sum + (rec.estimatedCost || 0), 0) || 0;

  return (
    <div className="max-w-6xl mx-auto p-4 md:p-6 font-shantell">
      {/* Header Section */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg p-6 mb-8">
        <div className="flex justify-between items-start mb-4">
          <div>
            <h1 className="text-3xl md:text-4xl font-bold mb-2">{travelPlan.planName}</h1>
            <p className="text-blue-100 text-lg mb-4">
              <i className="ri-map-pin-line mr-2"></i>
              {travelPlan.destination}
            </p>
          </div>
          <div className="text-right">
            <span className={`px-4 py-2 rounded-full text-sm font-medium ${
              travelPlan.status === 'Planning' ? 'bg-yellow-100 text-yellow-800' :
              travelPlan.status === 'Confirmed' ? 'bg-green-100 text-green-800' :
              'bg-gray-100 text-gray-800'
            }`}>
              {travelPlan.status}
            </span>
          </div>
        </div>
        
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
          <div className="bg-white/20 px-4 py-2 rounded-lg text-center">
            <i className="ri-calendar-line text-xl mb-1 block"></i>
            <div className="font-medium">Duration</div>
            <div>{duration} days</div>
          </div>
          <div className="bg-white/20 px-4 py-2 rounded-lg text-center">
            <i className="ri-money-dollar-circle-line text-xl mb-1 block"></i>
            <div className="font-medium">Budget</div>
            <div>{formatCurrency(travelPlan.budget)}</div>
          </div>
          <div className="bg-white/20 px-4 py-2 rounded-lg text-center">
            <i className="ri-user-line text-xl mb-1 block"></i>
            <div className="font-medium">Group Size</div>
            <div>{travelPlan.groupSize} {travelPlan.groupSize === 1 ? 'person' : 'people'}</div>
          </div>
          <div className="bg-white/20 px-4 py-2 rounded-lg text-center">
            <i className="ri-star-line text-xl mb-1 block"></i>
            <div className="font-medium">Style</div>
            <div className="capitalize">{travelPlan.travelStyle}</div>
          </div>
        </div>
        
        <div className="mt-4">
          <div className="text-sm font-medium mb-2">Travel Dates:</div>
          <div className="text-blue-100">
            {formatDate(travelPlan.startDate)} - {formatDate(travelPlan.endDate)}
          </div>
        </div>
      </div>

      {/* Budget Overview */}
      <div className="bg-white rounded-lg shadow-lg p-6 mb-8">
        <h2 className="text-2xl font-bold mb-6 flex items-center">
          <i className="ri-pie-chart-line text-blue-600 mr-2"></i>
          Budget Overview
        </h2>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center p-6 bg-blue-50 rounded-lg">
            <i className="ri-money-dollar-circle-line text-3xl text-blue-600 mb-2"></i>
            <h3 className="text-lg font-semibold text-blue-800">Total Budget</h3>
            <p className="text-3xl font-bold text-blue-600">{formatCurrency(travelPlan.budget)}</p>
          </div>
          
          <div className="text-center p-6 bg-green-50 rounded-lg">
            <i className="ri-calculator-line text-3xl text-green-600 mb-2"></i>
            <h3 className="text-lg font-semibold text-green-800">Estimated Costs</h3>
            <p className="text-3xl font-bold text-green-600">{formatCurrency(totalRecommendationCost)}</p>
          </div>
          
          <div className="text-center p-6 bg-purple-50 rounded-lg">
            <i className="ri-wallet-line text-3xl text-purple-600 mb-2"></i>
            <h3 className="text-lg font-semibold text-purple-800">Remaining</h3>
            <p className="text-3xl font-bold text-purple-600">
              {formatCurrency(travelPlan.budget - totalRecommendationCost)}
            </p>
          </div>
        </div>
      </div>

      {/* AI Recommendations */}
      <div className="bg-white rounded-lg shadow-lg p-6 mb-8">
        <h2 className="text-2xl font-bold mb-6 flex items-center">
          <i className="ri-robot-line text-blue-600 mr-2"></i>
          AI Travel Recommendations
        </h2>
        
        {travelPlan.recommendations && travelPlan.recommendations.length > 0 ? (
          <div className="space-y-6">
            {travelPlan.recommendations.map((rec, index) => (
              <div key={index} className="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
                <div className="flex justify-between items-start mb-4">
                  <div className="flex-1">
                    <h3 className="text-xl font-semibold text-gray-800 mb-2">{rec.title}</h3>
                    <p className="text-gray-600 mb-3">{rec.description}</p>
                  </div>
                  <div className="text-right ml-4">
                    <div className="text-2xl font-bold text-green-600 mb-1">
                      {formatCurrency(rec.estimatedCost)}
                    </div>
                    {rec.rating && (
                      <div className="flex items-center text-yellow-500">
                        <i className="ri-star-fill mr-1"></i>
                        <span className="font-medium">{rec.rating}</span>
                      </div>
                    )}
                  </div>
                </div>
                
                {rec.features && rec.features.length > 0 && (
                  <div className="mb-4">
                    <h4 className="font-medium text-gray-700 mb-2">Features included:</h4>
                    <div className="flex flex-wrap gap-2">
                      {rec.features.map((feature, idx) => (
                        <span key={idx} className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm">
                          {feature}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
                
                <div className="flex justify-between items-center text-sm text-gray-500">
                  <span className="bg-gray-100 px-3 py-1 rounded-full capitalize font-medium">
                    <i className="ri-bookmark-line mr-1"></i>
                    {rec.type}
                  </span>
                  <span>
                    <i className="ri-time-line mr-1"></i>
                    {rec.duration}
                  </span>
                  {rec.location && (
                    <span>
                      <i className="ri-map-pin-line mr-1"></i>
                      {rec.location}
                    </span>
                  )}
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <i className="ri-robot-line text-6xl text-gray-300 mb-4"></i>
            <h3 className="text-xl font-semibold text-gray-600 mb-2">No Recommendations Available</h3>
            <p className="text-gray-500">AI recommendations could not be generated for this plan.</p>
          </div>
        )}
      </div>

      {/* Travel Preferences */}
      {travelPlan.activities && travelPlan.activities.length > 0 && (
        <div className="bg-white rounded-lg shadow-lg p-6 mb-8">
          <h2 className="text-2xl font-bold mb-6 flex items-center">
            <i className="ri-heart-line text-blue-600 mr-2"></i>
            Your Travel Preferences
          </h2>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {travelPlan.activities.map((activity, index) => (
              <div key={index} className="bg-blue-50 text-blue-800 px-4 py-2 rounded-lg text-center font-medium capitalize">
                {activity}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex flex-col sm:flex-row gap-4 justify-center">
        <button
          onClick={() => navigate('/ai-travel-plans')}
          className="bg-gray-600 hover:bg-gray-700 text-white px-8 py-3 rounded-md font-medium flex items-center justify-center"
        >
          <i className="ri-arrow-left-line mr-2"></i>
          Back to All Plans
        </button>
        <button
          onClick={() => navigate('/ai-travel-planner')}
          className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-md font-medium flex items-center justify-center"
        >
          <i className="ri-add-line mr-2"></i>
          Create New Plan
        </button>
        <button
          onClick={() => window.print()}
          className="bg-green-600 hover:bg-green-700 text-white px-8 py-3 rounded-md font-medium flex items-center justify-center"
        >
          <i className="ri-printer-line mr-2"></i>
          Print Plan
        </button>
      </div>
    </div>
  );
};

export default AITravelPlanDetails;
