@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom base styles */
@layer base {
  * {
    @apply box-border;
  }

  body {
    @apply font-inter text-gray-900 antialiased;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-poppins font-semibold;
  }
}

/* Custom component styles */
@layer components {
  .btn-primary {
    @apply bg-gradient-to-r from-pink-500 to-purple-600 text-white px-6 py-3 rounded-lg font-medium hover:from-pink-600 hover:to-purple-700 transition-all duration-200 shadow-md hover:shadow-lg;
  }

  .btn-secondary {
    @apply bg-white text-gray-700 border border-gray-300 px-6 py-3 rounded-lg font-medium hover:bg-gray-50 transition-all duration-200 shadow-sm hover:shadow-md;
  }

  .card {
    @apply bg-white rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-shadow duration-200;
  }

  .input-field {
    @apply w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-all duration-200;
  }

  .gradient-bg {
    @apply bg-gradient-to-br from-pink-50 via-purple-50 to-indigo-50;
  }
}
@layer utilities {
    .scrollbar-medium::-webkit-scrollbar {
      width: 8px;
    }

    .line-clamp-2 {
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }

    .line-clamp-3 {
      display: -webkit-box;
      -webkit-line-clamp: 3;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }
  }
@layer utilities {
    
    @variants responsive {
        .masonry {
            column-gap: 1em;
            column-count: 1;
        }
        .masonry-sm {
            column-gap: 1em;
            column-count: 2;
        }
        .masonry-md {
            column-gap: 1em;
            column-count: 3;
        }
        .break-inside {
            break-inside: avoid;
        }

    }
}