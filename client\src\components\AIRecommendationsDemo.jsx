import React, { useState } from 'react';

const AIRecommendationsDemo = () => {
  const [showDemo, setShowDemo] = useState(false);

  const demoRecommendations = [
    {
      title: "Comfortable Accommodation",
      description: "Perfect mid-range hotel or accommodation in Paris, France for 2 people for 5 days.",
      type: "accommodation",
      estimatedCost: 800,
      duration: "5 nights",
      location: "Central Paris, France"
    },
    {
      title: "Transportation & Getting Around",
      description: "Complete transportation solution for Paris, France including airport transfers and local transport.",
      type: "transport",
      estimatedCost: 300,
      duration: "Throughout trip",
      location: "Paris, France city center"
    },
    {
      title: "Adventure & Outdoor Activities",
      description: "Exciting outdoor adventures in Paris, France including hiking, tours, and thrilling experiences perfect for your group.",
      type: "adventure",
      estimatedCost: 400,
      duration: "1-2 days",
      location: "Adventure areas in Paris, France"
    },
    {
      title: "Cultural Experiences & Museums",
      description: "Immerse yourself in the rich culture of Paris, France with museums, historical sites, and local traditions.",
      type: "cultural",
      estimatedCost: 300,
      duration: "1 day",
      location: "Historic district of Paris, France"
    },
    {
      title: "Local Cuisine & Food Tours",
      description: "Discover the authentic flavors of Paris, France with food tours, local restaurants, and culinary experiences.",
      type: "food",
      estimatedCost: 200,
      duration: "Multiple meals",
      location: "Food districts of Paris, France"
    }
  ];

  const totalCost = demoRecommendations.reduce((sum, rec) => sum + rec.estimatedCost, 0);
  const budget = 2000;

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  return (
    <div className="max-w-4xl mx-auto p-6 font-shantell">
      <div className="text-center mb-8">
        <h1 className="text-4xl font-bold text-gray-800 mb-2">
          <i className="ri-magic-line text-blue-600 mr-2"></i>
          AI Recommendations Demo
        </h1>
        <p className="text-gray-600 text-lg mb-4">
          See how our AI creates personalized travel recommendations
        </p>
        <button
          onClick={() => setShowDemo(!showDemo)}
          className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-bold py-3 px-8 rounded-full shadow-lg transform hover:scale-105 transition-all duration-200"
        >
          {showDemo ? 'Hide Demo' : 'Show AI Recommendations'}
        </button>
      </div>

      {showDemo && (
        <div className="space-y-6">
          {/* Sample Input */}
          <div className="bg-blue-50 rounded-lg p-6">
            <h3 className="text-xl font-bold mb-4 text-blue-800">Sample Input:</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div>
                <strong>Destination:</strong> Paris, France
              </div>
              <div>
                <strong>Budget:</strong> $2,000
              </div>
              <div>
                <strong>Duration:</strong> 5 days
              </div>
              <div>
                <strong>Group:</strong> 2 people
              </div>
              <div>
                <strong>Style:</strong> Mid-range
              </div>
              <div>
                <strong>Activities:</strong> Cultural, Food, Adventure
              </div>
            </div>
          </div>

          {/* AI Recommendations */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h3 className="text-xl font-bold mb-4 flex items-center">
              <i className="ri-lightbulb-line text-blue-600 mr-2"></i>
              AI Generated Recommendations
            </h3>
            
            <div className="space-y-4">
              {demoRecommendations.map((rec, index) => (
                <div key={index} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                  <div className="flex justify-between items-start mb-2">
                    <h4 className="font-semibold text-gray-800">{rec.title}</h4>
                    <span className="text-green-600 font-semibold text-sm">
                      {formatCurrency(rec.estimatedCost)}
                    </span>
                  </div>
                  <p className="text-gray-600 text-sm mb-2">{rec.description}</p>
                  <div className="flex justify-between items-center">
                    <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs capitalize">
                      {rec.type}
                    </span>
                    <span className="text-gray-500 text-xs">
                      <i className="ri-time-line mr-1"></i>
                      {rec.duration}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Budget Summary */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h3 className="text-xl font-bold mb-4 flex items-center">
              <i className="ri-money-dollar-circle-line text-blue-600 mr-2"></i>
              Budget Summary
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center p-4 bg-blue-50 rounded-lg">
                <h4 className="text-lg font-semibold text-blue-800">Total Budget</h4>
                <p className="text-2xl font-bold text-blue-600">
                  {formatCurrency(budget)}
                </p>
              </div>
              
              <div className="text-center p-4 bg-green-50 rounded-lg">
                <h4 className="text-lg font-semibold text-green-800">Estimated Costs</h4>
                <p className="text-2xl font-bold text-green-600">
                  {formatCurrency(totalCost)}
                </p>
              </div>
              
              <div className="text-center p-4 bg-gray-50 rounded-lg">
                <h4 className="text-lg font-semibold text-gray-800">Remaining</h4>
                <p className="text-2xl font-bold text-gray-600">
                  {formatCurrency(budget - totalCost)}
                </p>
              </div>
            </div>
          </div>

          {/* Success Message */}
          <div className="bg-green-50 border border-green-200 rounded-lg p-6 text-center">
            <i className="ri-check-circle-line text-4xl text-green-600 mb-2"></i>
            <h3 className="text-xl font-bold text-green-800 mb-2">AI Recommendations Working!</h3>
            <p className="text-green-700">
              The AI travel planner successfully generates personalized recommendations based on your preferences, 
              budget, and travel style. Each recommendation includes detailed descriptions, cost estimates, 
              and duration information to help you plan the perfect trip.
            </p>
          </div>
        </div>
      )}
    </div>
  );
};

export default AIRecommendationsDemo;
