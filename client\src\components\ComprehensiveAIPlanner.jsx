import React, { useState, useContext } from 'react';
import { AuthContext } from '../context/AuthContext';

const ComprehensiveAIPlanner = () => {
  const { currentUser } = useContext(AuthContext);
  const [step, setStep] = useState(1);
  const [formData, setFormData] = useState({
    destination: '',
    startDate: '',
    endDate: '',
    duration: '',
    budget: '',
    currency: 'USD',
    companionType: '',
    groupSize: 1,
    activityPreferences: [],
    dietaryOptions: [],
    specialRequirements: '',
    accommodationType: '',
    transportPreference: ''
  });
  
  const [loading, setLoading] = useState(false);
  const [itinerary, setItinerary] = useState(null);
  const [error, setError] = useState('');

  // Form options
  const companionTypes = [
    { id: 'solo', name: 'Solo Travel', icon: '🧳', description: 'Traveling alone' },
    { id: 'couple', name: 'Coup<PERSON>', icon: '💑', description: 'Romantic getaway for two' },
    { id: 'family', name: 'Family', icon: '👨‍👩‍👧‍👦', description: 'Family vacation with kids' },
    { id: 'friends', name: 'Friends', icon: '👥', description: 'Group of friends' },
    { id: 'business', name: 'Business', icon: '💼', description: 'Business travel' }
  ];

  const activityOptions = [
    { id: 'adventure', name: 'Adventure', icon: '🏔️', description: 'Hiking, climbing, extreme sports' },
    { id: 'cultural', name: 'Cultural', icon: '🏛️', description: 'Museums, history, art' },
    { id: 'food', name: 'Food & Dining', icon: '🍽️', description: 'Local cuisine, restaurants' },
    { id: 'shopping', name: 'Shopping', icon: '🛍️', description: 'Markets, malls, souvenirs' },
    { id: 'nightlife', name: 'Nightlife', icon: '🌃', description: 'Bars, clubs, entertainment' },
    { id: 'nature', name: 'Nature', icon: '🌿', description: 'Parks, wildlife, outdoors' },
    { id: 'beach', name: 'Beach & Water', icon: '🏖️', description: 'Swimming, water sports' },
    { id: 'wellness', name: 'Wellness & Spa', icon: '🧘‍♀️', description: 'Relaxation, spa treatments' },
    { id: 'photography', name: 'Photography', icon: '📸', description: 'Scenic spots, landmarks' },
    { id: 'sports', name: 'Sports', icon: '⚽', description: 'Sporting events, activities' }
  ];

  const dietaryOptions = [
    { id: 'none', name: 'No Restrictions', icon: '🍽️' },
    { id: 'vegetarian', name: 'Vegetarian', icon: '🥗' },
    { id: 'vegan', name: 'Vegan', icon: '🌱' },
    { id: 'halal', name: 'Halal', icon: '☪️' },
    { id: 'kosher', name: 'Kosher', icon: '✡️' },
    { id: 'gluten-free', name: 'Gluten-Free', icon: '🌾' },
    { id: 'dairy-free', name: 'Dairy-Free', icon: '🥛' },
    { id: 'nut-free', name: 'Nut-Free', icon: '🥜' }
  ];

  const accommodationTypes = [
    { id: 'hotel', name: 'Hotel', icon: '🏨' },
    { id: 'resort', name: 'Resort', icon: '🏖️' },
    { id: 'hostel', name: 'Hostel', icon: '🏠' },
    { id: 'airbnb', name: 'Airbnb/Rental', icon: '🏡' },
    { id: 'boutique', name: 'Boutique Hotel', icon: '🏛️' },
    { id: 'luxury', name: 'Luxury Hotel', icon: '👑' }
  ];

  const transportOptions = [
    { id: 'flight', name: 'Flight', icon: '✈️' },
    { id: 'train', name: 'Train', icon: '🚄' },
    { id: 'car', name: 'Car Rental', icon: '🚗' },
    { id: 'bus', name: 'Bus', icon: '🚌' },
    { id: 'mixed', name: 'Mixed Transport', icon: '🚊' }
  ];

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    setError('');
  };

  const handleArrayToggle = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: prev[field].includes(value)
        ? prev[field].filter(item => item !== value)
        : [...prev[field], value]
    }));
  };

  const calculateDuration = () => {
    if (formData.startDate && formData.endDate) {
      const start = new Date(formData.startDate);
      const end = new Date(formData.endDate);
      const diffTime = Math.abs(end - start);
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      setFormData(prev => ({ ...prev, duration: diffDays }));
    }
  };

  const nextStep = () => {
    if (step < 6) {
      setStep(step + 1);
    } else {
      generateItinerary();
    }
  };

  const prevStep = () => {
    if (step > 1) {
      setStep(step - 1);
    }
  };

  const canProceed = () => {
    switch (step) {
      case 1: return formData.destination.trim() !== '';
      case 2: return formData.startDate && formData.endDate && formData.budget;
      case 3: return formData.companionType !== '';
      case 4: return formData.activityPreferences.length > 0;
      case 5: return formData.dietaryOptions.length > 0;
      case 6: return formData.accommodationType && formData.transportPreference;
      default: return false;
    }
  };

  const generateItinerary = async () => {
    setLoading(true);
    try {
      // Calculate duration if not set
      if (!formData.duration && formData.startDate && formData.endDate) {
        calculateDuration();
      }
      
      // Simulate AI processing
      await new Promise(resolve => setTimeout(resolve, 4000));
      
      // Generate comprehensive itinerary (will be implemented in next step)
      const generatedItinerary = await generateComprehensiveItinerary(formData);
      setItinerary(generatedItinerary);
    } catch (err) {
      setError('Failed to generate itinerary. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Placeholder for comprehensive itinerary generation
  const generateComprehensiveItinerary = async (data) => {
    // This will be implemented in the next step
    return {
      destination: data.destination,
      duration: data.duration,
      budget: data.budget,
      companionType: data.companionType,
      days: []
    };
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto px-4">
          <div className="animate-spin rounded-full h-20 w-20 border-b-2 border-blue-600 mx-auto mb-6"></div>
          <h2 className="text-3xl font-bold text-gray-900 mb-4">Creating Your Perfect Itinerary</h2>
          <p className="text-gray-600 mb-6">Our AI is analyzing your preferences and crafting a personalized travel plan...</p>
          
          <div className="bg-white rounded-xl p-6 shadow-lg">
            <div className="space-y-3">
              <div className="flex items-center justify-center space-x-2 text-sm text-gray-600">
                <div className="w-3 h-3 bg-blue-500 rounded-full animate-pulse"></div>
                <span>Analyzing destination data</span>
              </div>
              <div className="flex items-center justify-center space-x-2 text-sm text-gray-600">
                <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse animation-delay-200"></div>
                <span>Finding best accommodations</span>
              </div>
              <div className="flex items-center justify-center space-x-2 text-sm text-gray-600">
                <div className="w-3 h-3 bg-purple-500 rounded-full animate-pulse animation-delay-400"></div>
                <span>Selecting restaurants & activities</span>
              </div>
              <div className="flex items-center justify-center space-x-2 text-sm text-gray-600">
                <div className="w-3 h-3 bg-orange-500 rounded-full animate-pulse animation-delay-600"></div>
                <span>Optimizing daily schedule</span>
              </div>
              <div className="flex items-center justify-center space-x-2 text-sm text-gray-600">
                <div className="w-3 h-3 bg-red-500 rounded-full animate-pulse animation-delay-800"></div>
                <span>Generating travel tips</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (itinerary) {
    // Itinerary display will be implemented in next step
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 p-8">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-8">
            <h1 className="text-4xl font-bold text-gray-900 mb-2">
              Your {itinerary.duration}-Day {itinerary.destination} Adventure
            </h1>
            <p className="text-gray-600">
              Personalized itinerary for {itinerary.companionType} travel • Budget: ${itinerary.budget}
            </p>
          </div>
          
          <div className="bg-white rounded-xl shadow-lg p-8 text-center">
            <h3 className="text-xl font-semibold mb-4">Comprehensive Itinerary Generated!</h3>
            <p className="text-gray-600 mb-6">
              Your detailed day-by-day plan with hotels, restaurants, activities, and travel tips is ready.
            </p>
            <button
              onClick={() => {
                setItinerary(null);
                setStep(1);
                setFormData({
                  destination: '',
                  startDate: '',
                  endDate: '',
                  duration: '',
                  budget: '',
                  currency: 'USD',
                  companionType: '',
                  groupSize: 1,
                  activityPreferences: [],
                  dietaryOptions: [],
                  specialRequirements: '',
                  accommodationType: '',
                  transportPreference: ''
                });
              }}
              className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg transition-colors"
            >
              Plan Another Trip
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 py-8 px-4">
      <div className="max-w-3xl mx-auto">
        {/* Progress Bar */}
        <div className="mb-8">
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm font-medium text-gray-700">Step {step} of 6</span>
            <span className="text-sm text-gray-500">{Math.round((step / 6) * 100)}% Complete</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-3">
            <div 
              className="bg-gradient-to-r from-blue-600 to-purple-600 h-3 rounded-full transition-all duration-500"
              style={{ width: `${(step / 6) * 100}%` }}
            ></div>
          </div>
        </div>

        <div className="bg-white rounded-2xl shadow-xl p-8">
          {/* Step 1: Destination */}
          {step === 1 && (
            <div className="text-center">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                Where would you like to go?
              </h2>
              <p className="text-gray-600 mb-8">
                Enter your dream destination to start planning your perfect trip
              </p>
              <div className="relative max-w-md mx-auto">
                <i className="ri-map-pin-line absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 text-xl"></i>
                <input
                  type="text"
                  placeholder="e.g., Paris, Tokyo, New York"
                  value={formData.destination}
                  onChange={(e) => handleInputChange('destination', e.target.value)}
                  className="w-full pl-12 pr-4 py-4 border-2 border-gray-200 rounded-xl text-lg focus:border-blue-500 focus:outline-none"
                />
              </div>
            </div>
          )}

          {/* Navigation Buttons */}
          <div className="flex justify-between mt-8">
            <button
              onClick={prevStep}
              disabled={step === 1}
              className={`px-6 py-3 rounded-lg font-medium transition-colors ${
                step === 1 
                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed' 
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
            >
              Previous
            </button>
            <button
              onClick={nextStep}
              disabled={!canProceed()}
              className={`px-8 py-3 rounded-lg font-medium transition-colors ${
                canProceed()
                  ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white hover:from-blue-700 hover:to-purple-700'
                  : 'bg-gray-200 text-gray-400 cursor-not-allowed'
              }`}
            >
              {step === 6 ? 'Generate Itinerary' : 'Next'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ComprehensiveAIPlanner;
