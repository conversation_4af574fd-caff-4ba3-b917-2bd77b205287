import React, { useState, useContext } from 'react';
import { AuthContext } from '../context/AuthContext';

const ComprehensiveAIPlanner = () => {
  const { currentUser } = useContext(AuthContext);
  const [step, setStep] = useState(1);
  const [formData, setFormData] = useState({
    destination: '',
    startDate: '',
    endDate: '',
    duration: '',
    budget: '',
    currency: 'USD',
    companionType: '',
    groupSize: 1,
    activityPreferences: [],
    dietaryOptions: [],
    specialRequirements: '',
    accommodationType: '',
    transportPreference: ''
  });
  
  const [loading, setLoading] = useState(false);
  const [itinerary, setItinerary] = useState(null);
  const [error, setError] = useState('');

  // Form options
  const companionTypes = [
    { id: 'solo', name: 'Solo Travel', icon: '🧳', description: 'Traveling alone' },
    { id: 'couple', name: 'Coup<PERSON>', icon: '💑', description: 'Romantic getaway for two' },
    { id: 'family', name: 'Family', icon: '👨‍👩‍👧‍👦', description: 'Family vacation with kids' },
    { id: 'friends', name: 'Friends', icon: '👥', description: 'Group of friends' },
    { id: 'business', name: 'Business', icon: '💼', description: 'Business travel' }
  ];

  const activityOptions = [
    { id: 'adventure', name: 'Adventure', icon: '🏔️', description: 'Hiking, climbing, extreme sports' },
    { id: 'cultural', name: 'Cultural', icon: '🏛️', description: 'Museums, history, art' },
    { id: 'food', name: 'Food & Dining', icon: '🍽️', description: 'Local cuisine, restaurants' },
    { id: 'shopping', name: 'Shopping', icon: '🛍️', description: 'Markets, malls, souvenirs' },
    { id: 'nightlife', name: 'Nightlife', icon: '🌃', description: 'Bars, clubs, entertainment' },
    { id: 'nature', name: 'Nature', icon: '🌿', description: 'Parks, wildlife, outdoors' },
    { id: 'beach', name: 'Beach & Water', icon: '🏖️', description: 'Swimming, water sports' },
    { id: 'wellness', name: 'Wellness & Spa', icon: '🧘‍♀️', description: 'Relaxation, spa treatments' },
    { id: 'photography', name: 'Photography', icon: '📸', description: 'Scenic spots, landmarks' },
    { id: 'sports', name: 'Sports', icon: '⚽', description: 'Sporting events, activities' }
  ];

  const dietaryOptions = [
    { id: 'none', name: 'No Restrictions', icon: '🍽️' },
    { id: 'vegetarian', name: 'Vegetarian', icon: '🥗' },
    { id: 'vegan', name: 'Vegan', icon: '🌱' },
    { id: 'halal', name: 'Halal', icon: '☪️' },
    { id: 'kosher', name: 'Kosher', icon: '✡️' },
    { id: 'gluten-free', name: 'Gluten-Free', icon: '🌾' },
    { id: 'dairy-free', name: 'Dairy-Free', icon: '🥛' },
    { id: 'nut-free', name: 'Nut-Free', icon: '🥜' }
  ];

  const accommodationTypes = [
    { id: 'hotel', name: 'Hotel', icon: '🏨' },
    { id: 'resort', name: 'Resort', icon: '🏖️' },
    { id: 'hostel', name: 'Hostel', icon: '🏠' },
    { id: 'airbnb', name: 'Airbnb/Rental', icon: '🏡' },
    { id: 'boutique', name: 'Boutique Hotel', icon: '🏛️' },
    { id: 'luxury', name: 'Luxury Hotel', icon: '👑' }
  ];

  const transportOptions = [
    { id: 'flight', name: 'Flight', icon: '✈️' },
    { id: 'train', name: 'Train', icon: '🚄' },
    { id: 'car', name: 'Car Rental', icon: '🚗' },
    { id: 'bus', name: 'Bus', icon: '🚌' },
    { id: 'mixed', name: 'Mixed Transport', icon: '🚊' }
  ];

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    setError('');
  };

  const handleArrayToggle = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: prev[field].includes(value)
        ? prev[field].filter(item => item !== value)
        : [...prev[field], value]
    }));
  };

  const calculateDuration = () => {
    if (formData.startDate && formData.endDate) {
      const start = new Date(formData.startDate);
      const end = new Date(formData.endDate);
      const diffTime = Math.abs(end - start);
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      setFormData(prev => ({ ...prev, duration: diffDays }));
    }
  };

  const nextStep = () => {
    if (step < 6) {
      setStep(step + 1);
    } else {
      generateItinerary();
    }
  };

  const prevStep = () => {
    if (step > 1) {
      setStep(step - 1);
    }
  };

  const canProceed = () => {
    switch (step) {
      case 1: return formData.destination.trim() !== '';
      case 2: return formData.startDate && formData.endDate && formData.budget;
      case 3: return formData.companionType !== '';
      case 4: return formData.activityPreferences.length > 0;
      case 5: return formData.dietaryOptions.length > 0;
      case 6: return formData.accommodationType && formData.transportPreference;
      default: return false;
    }
  };

  const generateItinerary = async () => {
    setLoading(true);
    try {
      // Calculate duration if not set
      if (!formData.duration && formData.startDate && formData.endDate) {
        calculateDuration();
      }
      
      // Simulate AI processing
      await new Promise(resolve => setTimeout(resolve, 4000));
      
      // Generate comprehensive itinerary (will be implemented in next step)
      const generatedItinerary = await generateComprehensiveItinerary(formData);
      setItinerary(generatedItinerary);
    } catch (err) {
      setError('Failed to generate itinerary. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Comprehensive AI itinerary generation
  const generateComprehensiveItinerary = async (data) => {
    const {
      destination,
      duration,
      budget,
      companionType,
      groupSize,
      activityPreferences,
      dietaryOptions,
      accommodationType,
      transportPreference,
      specialRequirements
    } = data;

    // Get destination-specific data
    const destinationData = getDestinationData(destination);

    // Calculate budget breakdown
    const budgetBreakdown = calculateBudgetBreakdown(budget, duration, companionType);

    // Generate accommodation suggestions
    const accommodations = generateAccommodationSuggestions(destination, accommodationType, budgetBreakdown.accommodation, companionType, groupSize);

    // Generate day-by-day itinerary
    const days = [];
    for (let i = 1; i <= duration; i++) {
      const day = await generateDayItinerary(
        destination,
        i,
        duration,
        activityPreferences,
        dietaryOptions,
        budgetBreakdown.dailyBudget,
        companionType,
        groupSize,
        destinationData
      );
      days.push(day);
    }

    // Generate travel tips
    const travelTips = generateTravelTips(destination, companionType, activityPreferences, dietaryOptions);

    // Generate restaurant suggestions
    const restaurants = generateRestaurantSuggestions(destination, dietaryOptions, budgetBreakdown.food, companionType);

    return {
      destination,
      duration,
      budget,
      companionType,
      groupSize,
      activityPreferences,
      dietaryOptions,
      accommodationType,
      transportPreference,
      days,
      accommodations,
      restaurants,
      budgetBreakdown,
      travelTips,
      destinationData
    };
  };

  // Get destination-specific information
  const getDestinationData = (destination) => {
    const destinations = {
      'paris': {
        highlights: ['Eiffel Tower', 'Louvre Museum', 'Notre-Dame', 'Champs-Élysées', 'Montmartre'],
        cuisine: ['French pastries', 'Wine', 'Cheese', 'Croissants', 'Macarons'],
        culture: ['Art museums', 'Gothic architecture', 'Fashion', 'Café culture'],
        bestTime: 'April-June, September-October',
        currency: 'EUR',
        language: 'French',
        timeZone: 'CET'
      },
      'tokyo': {
        highlights: ['Shibuya Crossing', 'Senso-ji Temple', 'Tokyo Skytree', 'Tsukiji Market', 'Harajuku'],
        cuisine: ['Sushi', 'Ramen', 'Tempura', 'Sake', 'Wagyu beef'],
        culture: ['Traditional temples', 'Modern technology', 'Anime culture', 'Tea ceremony'],
        bestTime: 'March-May, September-November',
        currency: 'JPY',
        language: 'Japanese',
        timeZone: 'JST'
      },
      'new york': {
        highlights: ['Times Square', 'Central Park', 'Statue of Liberty', 'Brooklyn Bridge', 'Empire State Building'],
        cuisine: ['Pizza', 'Bagels', 'Deli sandwiches', 'Cheesecake', 'Hot dogs'],
        culture: ['Broadway shows', 'Museums', 'Street art', 'Diverse neighborhoods'],
        bestTime: 'April-June, September-November',
        currency: 'USD',
        language: 'English',
        timeZone: 'EST'
      }
    };

    const key = destination.toLowerCase();
    return destinations[key] || {
      highlights: [`${destination} landmarks`, `${destination} attractions`],
      cuisine: ['Local specialties', 'Traditional dishes'],
      culture: ['Local traditions', 'Historical sites'],
      bestTime: 'Year-round',
      currency: 'Local currency',
      language: 'Local language',
      timeZone: 'Local time'
    };
  };

  // Calculate budget breakdown
  const calculateBudgetBreakdown = (totalBudget, duration, companionType) => {
    const multiplier = companionType === 'luxury' ? 1.5 : companionType === 'budget' ? 0.7 : 1;

    return {
      accommodation: Math.floor(totalBudget * 0.35 * multiplier),
      food: Math.floor(totalBudget * 0.30),
      activities: Math.floor(totalBudget * 0.25),
      transport: Math.floor(totalBudget * 0.10),
      dailyBudget: Math.floor(totalBudget / duration)
    };
  };

  // Generate accommodation suggestions
  const generateAccommodationSuggestions = (destination, type, budget, companionType, groupSize) => {
    const accommodations = [];
    const pricePerNight = Math.floor(budget / 7); // Assuming 7 nights average

    const hotelTypes = {
      'hotel': ['Grand Hotel', 'City Center Hotel', 'Business Hotel'],
      'resort': ['Luxury Resort', 'Beach Resort', 'Spa Resort'],
      'hostel': ['Backpacker Hostel', 'Youth Hostel', 'Budget Hostel'],
      'airbnb': ['Cozy Apartment', 'Local Home', 'Studio Rental'],
      'boutique': ['Boutique Hotel', 'Design Hotel', 'Historic Hotel'],
      'luxury': ['5-Star Hotel', 'Luxury Suite', 'Premium Resort']
    };

    const names = hotelTypes[type] || hotelTypes['hotel'];

    for (let i = 0; i < 3; i++) {
      accommodations.push({
        name: `${names[i]} ${destination}`,
        type: type,
        pricePerNight: pricePerNight + (i * 20),
        rating: 4.2 + (i * 0.3),
        amenities: getAmenities(type, companionType),
        location: i === 0 ? 'City Center' : i === 1 ? 'Tourist District' : 'Local Neighborhood',
        suitableFor: companionType,
        capacity: groupSize
      });
    }

    return accommodations;
  };

  // Get amenities based on accommodation type and companion type
  const getAmenities = (type, companionType) => {
    const baseAmenities = ['WiFi', 'Air Conditioning', '24/7 Reception'];

    if (type === 'luxury') {
      return [...baseAmenities, 'Spa', 'Fine Dining', 'Concierge', 'Room Service'];
    } else if (type === 'resort') {
      return [...baseAmenities, 'Pool', 'Beach Access', 'Restaurant', 'Activities'];
    } else if (companionType === 'family') {
      return [...baseAmenities, 'Family Rooms', 'Kids Club', 'Pool', 'Restaurant'];
    } else if (companionType === 'business') {
      return [...baseAmenities, 'Business Center', 'Meeting Rooms', 'Express Check-in'];
    }

    return baseAmenities;
  };

  // Generate restaurant suggestions
  const generateRestaurantSuggestions = (destination, dietaryOptions, foodBudget, companionType) => {
    const restaurants = [];
    const mealBudget = Math.floor(foodBudget / 21); // 3 meals × 7 days

    const cuisineTypes = ['Local Cuisine', 'International', 'Fine Dining', 'Casual Dining', 'Street Food'];

    cuisineTypes.forEach((cuisine, index) => {
      restaurants.push({
        name: `${cuisine} Restaurant ${destination}`,
        cuisine: cuisine,
        priceRange: mealBudget + (index * 10),
        rating: 4.0 + (index * 0.2),
        dietaryFriendly: getDietaryCompatibility(dietaryOptions),
        atmosphere: getAtmosphere(companionType, cuisine),
        specialties: getSpecialties(destination, cuisine),
        location: index % 2 === 0 ? 'City Center' : 'Local District'
      });
    });

    return restaurants;
  };

  // Check dietary compatibility
  const getDietaryCompatibility = (dietaryOptions) => {
    if (dietaryOptions.includes('none')) return ['All diets welcome'];

    const compatibility = [];
    if (dietaryOptions.includes('vegetarian')) compatibility.push('Vegetarian options');
    if (dietaryOptions.includes('vegan')) compatibility.push('Vegan menu');
    if (dietaryOptions.includes('halal')) compatibility.push('Halal certified');
    if (dietaryOptions.includes('kosher')) compatibility.push('Kosher available');
    if (dietaryOptions.includes('gluten-free')) compatibility.push('Gluten-free options');

    return compatibility.length > 0 ? compatibility : ['Standard menu'];
  };

  // Get atmosphere based on companion type
  const getAtmosphere = (companionType, cuisine) => {
    if (companionType === 'couple') return 'Romantic';
    if (companionType === 'family') return 'Family-friendly';
    if (companionType === 'business') return 'Professional';
    if (cuisine === 'Fine Dining') return 'Elegant';
    return 'Casual';
  };

  // Get specialties based on destination and cuisine
  const getSpecialties = (destination, cuisine) => {
    const destinationSpecialties = {
      'paris': ['Coq au Vin', 'Bouillabaisse', 'Crème Brûlée'],
      'tokyo': ['Sushi Omakase', 'Ramen', 'Tempura'],
      'new york': ['NY Pizza', 'Bagels', 'Cheesecake']
    };

    const key = destination.toLowerCase();
    return destinationSpecialties[key] || ['Local specialties', 'Traditional dishes', 'Seasonal menu'];
  };

  // Generate day itinerary
  const generateDayItinerary = async (destination, dayNumber, totalDays, activities, dietary, dailyBudget, companionType, groupSize, destinationData) => {
    const isFirstDay = dayNumber === 1;
    const isLastDay = dayNumber === totalDays;

    const dayActivities = [];

    if (isFirstDay) {
      // Arrival day
      dayActivities.push({
        time: '10:00 AM',
        title: `Arrival in ${destination}`,
        description: 'Airport pickup, check-in to accommodation, and city orientation',
        type: 'logistics',
        cost: Math.floor(dailyBudget * 0.1),
        duration: '3 hours',
        tips: 'Keep important documents handy and exchange some local currency'
      });

      dayActivities.push({
        time: '2:00 PM',
        title: 'Welcome Lunch',
        description: `First taste of ${destination} cuisine at a local restaurant`,
        type: 'food',
        cost: Math.floor(dailyBudget * 0.2),
        duration: '1.5 hours',
        tips: 'Perfect introduction to local flavors'
      });

      dayActivities.push({
        time: '4:00 PM',
        title: 'Neighborhood Walk',
        description: 'Gentle exploration of your accommodation area',
        type: 'sightseeing',
        cost: 0,
        duration: '2 hours',
        tips: 'Great for beating jet lag and getting oriented'
      });
    } else if (isLastDay) {
      // Departure day
      dayActivities.push({
        time: '9:00 AM',
        title: 'Last-minute Shopping',
        description: 'Souvenir shopping and final city exploration',
        type: 'shopping',
        cost: Math.floor(dailyBudget * 0.3),
        duration: '2 hours',
        tips: 'Check customs regulations for souvenirs'
      });

      dayActivities.push({
        time: '12:00 PM',
        title: 'Farewell Meal',
        description: 'Final meal featuring your favorite local dishes',
        type: 'food',
        cost: Math.floor(dailyBudget * 0.25),
        duration: '1.5 hours',
        tips: 'Savor the last moments of your journey'
      });

      dayActivities.push({
        time: '3:00 PM',
        title: 'Departure',
        description: 'Check-out and airport transfer',
        type: 'logistics',
        cost: Math.floor(dailyBudget * 0.1),
        duration: '2 hours',
        tips: 'Arrive at airport 2-3 hours before international flights'
      });
    } else {
      // Regular exploration day
      const morningActivity = generateActivityByPreference(destination, activities, 'morning', dailyBudget, destinationData);
      const lunchActivity = generateMealActivity(destination, dietary, dailyBudget, 'lunch');
      const afternoonActivity = generateActivityByPreference(destination, activities, 'afternoon', dailyBudget, destinationData);
      const dinnerActivity = generateMealActivity(destination, dietary, dailyBudget, 'dinner');

      dayActivities.push(morningActivity, lunchActivity, afternoonActivity, dinnerActivity);
    }

    return {
      day: dayNumber,
      title: isFirstDay ? `Welcome to ${destination}` :
             isLastDay ? `Farewell ${destination}` :
             `Exploring ${destination}`,
      activities: dayActivities,
      totalCost: dayActivities.reduce((sum, activity) => sum + activity.cost, 0),
      weather: getWeatherTip(destination, dayNumber),
      tips: getDayTips(destination, dayNumber, totalDays, companionType)
    };
  };

  // Generate activity based on preferences
  const generateActivityByPreference = (destination, preferences, timeOfDay, dailyBudget, destinationData) => {
    const time = timeOfDay === 'morning' ? '9:00 AM' : '2:00 PM';
    const costMultiplier = timeOfDay === 'morning' ? 0.3 : 0.25;

    if (preferences.includes('cultural')) {
      return {
        time,
        title: `${destinationData.highlights[0]} Cultural Tour`,
        description: `Explore ${destinationData.culture[0]} and historical landmarks`,
        type: 'cultural',
        cost: Math.floor(dailyBudget * costMultiplier),
        duration: '3 hours',
        tips: 'Book skip-the-line tickets in advance'
      };
    } else if (preferences.includes('adventure')) {
      return {
        time,
        title: `${destination} Adventure Experience`,
        description: 'Outdoor adventure activity and scenic exploration',
        type: 'adventure',
        cost: Math.floor(dailyBudget * (costMultiplier + 0.1)),
        duration: '4 hours',
        tips: 'Wear comfortable shoes and bring water'
      };
    } else if (preferences.includes('shopping')) {
      return {
        time,
        title: `${destination} Shopping District`,
        description: 'Explore local markets, boutiques, and shopping areas',
        type: 'shopping',
        cost: Math.floor(dailyBudget * costMultiplier),
        duration: '3 hours',
        tips: 'Bargain at local markets for better prices'
      };
    } else {
      return {
        time,
        title: `${destinationData.highlights[1] || destination} Highlights`,
        description: 'Visit must-see attractions and landmarks',
        type: 'sightseeing',
        cost: Math.floor(dailyBudget * costMultiplier),
        duration: '3 hours',
        tips: 'Perfect for photos and learning local history'
      };
    }
  };

  // Generate meal activity
  const generateMealActivity = (destination, dietary, dailyBudget, mealType) => {
    const isLunch = mealType === 'lunch';
    const time = isLunch ? '12:30 PM' : '7:00 PM';
    const costMultiplier = isLunch ? 0.15 : 0.2;

    const dietaryText = dietary.includes('vegetarian') ? 'vegetarian-friendly' :
                       dietary.includes('vegan') ? 'vegan' :
                       dietary.includes('halal') ? 'halal' : 'local';

    return {
      time,
      title: `${mealType.charAt(0).toUpperCase() + mealType.slice(1)} Experience`,
      description: `${dietaryText} ${destination} cuisine at a recommended restaurant`,
      type: 'food',
      cost: Math.floor(dailyBudget * costMultiplier),
      duration: isLunch ? '1 hour' : '2 hours',
      tips: isLunch ? 'Try local lunch specials' : 'Make reservations for popular restaurants'
    };
  };

  // Generate travel tips
  const generateTravelTips = (destination, companionType, activities, dietary) => {
    const tips = [
      {
        category: 'Transportation',
        tip: `Use local public transport or ride-sharing apps in ${destination}`,
        icon: 'ri-bus-line'
      },
      {
        category: 'Money',
        tip: 'Notify your bank about travel plans and keep some local currency',
        icon: 'ri-money-dollar-circle-line'
      },
      {
        category: 'Safety',
        tip: 'Keep copies of important documents and stay aware of surroundings',
        icon: 'ri-shield-check-line'
      },
      {
        category: 'Culture',
        tip: `Learn basic local phrases and respect ${destination}'s customs`,
        icon: 'ri-global-line'
      }
    ];

    // Add companion-specific tips
    if (companionType === 'family') {
      tips.push({
        category: 'Family Travel',
        tip: 'Pack snacks and entertainment for kids during long activities',
        icon: 'ri-group-line'
      });
    } else if (companionType === 'couple') {
      tips.push({
        category: 'Romantic Travel',
        tip: 'Book romantic restaurants and sunset activities in advance',
        icon: 'ri-heart-line'
      });
    }

    // Add dietary tips
    if (dietary.includes('vegetarian') || dietary.includes('vegan')) {
      tips.push({
        category: 'Dietary',
        tip: 'Download translation apps for dietary restrictions',
        icon: 'ri-leaf-line'
      });
    }

    return tips;
  };

  // Get weather tip
  const getWeatherTip = (destination, day) => {
    const tips = [
      'Check weather forecast and dress in layers',
      'Perfect day for outdoor activities',
      'Great weather for sightseeing and walking',
      'Ideal conditions for photography',
      'Good day to explore local neighborhoods'
    ];
    return tips[day % tips.length];
  };

  // Get day-specific tips
  const getDayTips = (destination, day, totalDays, companionType) => {
    if (day === 1) return 'Take it easy on your first day to adjust to the new environment';
    if (day === totalDays) return 'Pack early and double-check your departure details';
    if (day === Math.ceil(totalDays / 2)) return 'Perfect time to try something adventurous!';
    if (companionType === 'family') return 'Plan rest breaks between activities for kids';
    return 'Stay hydrated and take breaks when needed';
  };

  // Get activity icon
  const getActivityIcon = (type) => {
    switch (type) {
      case 'cultural': return 'ri-building-line';
      case 'food': return 'ri-restaurant-line';
      case 'adventure': return 'ri-mountain-line';
      case 'shopping': return 'ri-shopping-bag-line';
      case 'sightseeing': return 'ri-camera-line';
      case 'nightlife': return 'ri-music-line';
      case 'logistics': return 'ri-plane-line';
      case 'nature': return 'ri-leaf-line';
      case 'beach': return 'ri-sun-line';
      case 'wellness': return 'ri-heart-pulse-line';
      case 'sports': return 'ri-football-line';
      default: return 'ri-map-pin-line';
    }
  };

  // Save itinerary function
  const saveItinerary = async (itinerary) => {
    try {
      if (!currentUser) {
        alert('Please login to save your itinerary');
        return;
      }

      // Save to localStorage for now (can be enhanced to save to backend)
      const savedItineraries = JSON.parse(localStorage.getItem('savedItineraries') || '[]');
      const itineraryToSave = {
        id: Date.now(),
        ...itinerary,
        savedAt: new Date().toISOString(),
        userId: currentUser.data._id
      };

      savedItineraries.push(itineraryToSave);
      localStorage.setItem('savedItineraries', JSON.stringify(savedItineraries));

      alert('Itinerary saved successfully!');
    } catch (error) {
      alert('Failed to save itinerary. Please try again.');
    }
  };

  // Download PDF function
  const downloadPDF = (itinerary) => {
    try {
      // Create a comprehensive text version for download
      let content = `${itinerary.destination} Travel Itinerary\n`;
      content += `Duration: ${itinerary.duration} days\n`;
      content += `Budget: $${itinerary.budget}\n`;
      content += `Travel Style: ${itinerary.companionType}\n`;
      content += `Group Size: ${itinerary.groupSize}\n\n`;

      content += `BUDGET BREAKDOWN:\n`;
      content += `Accommodation: $${itinerary.budgetBreakdown.accommodation}\n`;
      content += `Food: $${itinerary.budgetBreakdown.food}\n`;
      content += `Activities: $${itinerary.budgetBreakdown.activities}\n`;
      content += `Transport: $${itinerary.budgetBreakdown.transport}\n\n`;

      content += `RECOMMENDED ACCOMMODATIONS:\n`;
      itinerary.accommodations.forEach((hotel, index) => {
        content += `${index + 1}. ${hotel.name} - $${hotel.pricePerNight}/night\n`;
        content += `   Rating: ${hotel.rating} | Location: ${hotel.location}\n`;
        content += `   Amenities: ${hotel.amenities.join(', ')}\n\n`;
      });

      content += `RESTAURANT RECOMMENDATIONS:\n`;
      itinerary.restaurants.forEach((restaurant, index) => {
        content += `${index + 1}. ${restaurant.name} - ${restaurant.cuisine}\n`;
        content += `   Price: $${restaurant.priceRange} | Rating: ${restaurant.rating}\n`;
        content += `   Dietary: ${restaurant.dietaryFriendly.join(', ')}\n\n`;
      });

      content += `DAY-BY-DAY ITINERARY:\n`;
      itinerary.days.forEach(day => {
        content += `\nDAY ${day.day}: ${day.title}\n`;
        content += `Daily Budget: $${day.totalCost}\n`;
        content += `Weather: ${day.weather}\n\n`;

        day.activities.forEach(activity => {
          content += `${activity.time} - ${activity.title}\n`;
          content += `${activity.description}\n`;
          content += `Cost: $${activity.cost} | Duration: ${activity.duration}\n`;
          if (activity.tips) content += `Tip: ${activity.tips}\n`;
          content += `\n`;
        });

        if (day.tips) content += `Day Tip: ${day.tips}\n`;
      });

      content += `\nTRAVEL TIPS:\n`;
      itinerary.travelTips.forEach(tip => {
        content += `${tip.category}: ${tip.tip}\n`;
      });

      // Create and download file
      const blob = new Blob([content], { type: 'text/plain' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${itinerary.destination}-itinerary.txt`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);

      alert('Itinerary downloaded successfully!');
    } catch (error) {
      alert('Failed to download itinerary. Please try again.');
    }
  };

  // Share itinerary function
  const shareItinerary = async (itinerary) => {
    try {
      const shareText = `Check out my ${itinerary.duration}-day trip to ${itinerary.destination}!

Budget: $${itinerary.budget}
Travel Style: ${itinerary.companionType}
Activities: ${itinerary.activityPreferences.join(', ')}

Generated with AI Travel Planner`;

      if (navigator.share) {
        // Use native sharing if available
        await navigator.share({
          title: `${itinerary.destination} Travel Itinerary`,
          text: shareText,
          url: window.location.href
        });
      } else {
        // Fallback to clipboard
        await navigator.clipboard.writeText(shareText);
        alert('Itinerary details copied to clipboard!');
      }
    } catch (error) {
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = `My ${itinerary.duration}-day trip to ${itinerary.destination} - Budget: $${itinerary.budget}`;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      alert('Itinerary details copied to clipboard!');
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto px-4">
          <div className="animate-spin rounded-full h-20 w-20 border-b-2 border-blue-600 mx-auto mb-6"></div>
          <h2 className="text-3xl font-bold text-gray-900 mb-4">Creating Your Perfect Itinerary</h2>
          <p className="text-gray-600 mb-6">Our AI is analyzing your preferences and crafting a personalized travel plan...</p>
          
          <div className="bg-white rounded-xl p-6 shadow-lg">
            <div className="space-y-3">
              <div className="flex items-center justify-center space-x-2 text-sm text-gray-600">
                <div className="w-3 h-3 bg-blue-500 rounded-full animate-pulse"></div>
                <span>Analyzing destination data</span>
              </div>
              <div className="flex items-center justify-center space-x-2 text-sm text-gray-600">
                <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse animation-delay-200"></div>
                <span>Finding best accommodations</span>
              </div>
              <div className="flex items-center justify-center space-x-2 text-sm text-gray-600">
                <div className="w-3 h-3 bg-purple-500 rounded-full animate-pulse animation-delay-400"></div>
                <span>Selecting restaurants & activities</span>
              </div>
              <div className="flex items-center justify-center space-x-2 text-sm text-gray-600">
                <div className="w-3 h-3 bg-orange-500 rounded-full animate-pulse animation-delay-600"></div>
                <span>Optimizing daily schedule</span>
              </div>
              <div className="flex items-center justify-center space-x-2 text-sm text-gray-600">
                <div className="w-3 h-3 bg-red-500 rounded-full animate-pulse animation-delay-800"></div>
                <span>Generating travel tips</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (itinerary) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 p-4 md:p-8">
        <div className="max-w-7xl mx-auto">
          {/* Header */}
          <div className="text-center mb-8">
            <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-2">
              Your {itinerary.duration}-Day {itinerary.destination} Adventure
            </h1>
            <p className="text-gray-600 mb-4">
              Personalized itinerary for {itinerary.companionType} travel • Budget: ${itinerary.budget}
            </p>
            <div className="flex flex-wrap justify-center gap-2 text-sm">
              <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full">
                {itinerary.groupSize} {itinerary.groupSize === 1 ? 'person' : 'people'}
              </span>
              <span className="bg-green-100 text-green-800 px-3 py-1 rounded-full">
                {itinerary.accommodationType}
              </span>
              <span className="bg-purple-100 text-purple-800 px-3 py-1 rounded-full">
                {itinerary.activityPreferences.join(', ')}
              </span>
            </div>
          </div>

          {/* Budget Breakdown */}
          <div className="bg-white rounded-xl shadow-lg p-6 mb-8">
            <h3 className="text-xl font-bold mb-4">
              <i className="ri-money-dollar-circle-line mr-2 text-green-600"></i>
              Budget Breakdown
            </h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center p-4 bg-blue-50 rounded-lg">
                <div className="text-2xl font-bold text-blue-600">${itinerary.budgetBreakdown.accommodation}</div>
                <div className="text-sm text-gray-600">Accommodation</div>
              </div>
              <div className="text-center p-4 bg-green-50 rounded-lg">
                <div className="text-2xl font-bold text-green-600">${itinerary.budgetBreakdown.food}</div>
                <div className="text-sm text-gray-600">Food & Dining</div>
              </div>
              <div className="text-center p-4 bg-purple-50 rounded-lg">
                <div className="text-2xl font-bold text-purple-600">${itinerary.budgetBreakdown.activities}</div>
                <div className="text-sm text-gray-600">Activities</div>
              </div>
              <div className="text-center p-4 bg-orange-50 rounded-lg">
                <div className="text-2xl font-bold text-orange-600">${itinerary.budgetBreakdown.transport}</div>
                <div className="text-sm text-gray-600">Transport</div>
              </div>
            </div>
          </div>

          {/* Accommodation Suggestions */}
          <div className="bg-white rounded-xl shadow-lg p-6 mb-8">
            <h3 className="text-xl font-bold mb-4">
              <i className="ri-hotel-line mr-2 text-blue-600"></i>
              Recommended Accommodations
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {itinerary.accommodations.map((hotel, index) => (
                <div key={index} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                  <div className="flex justify-between items-start mb-2">
                    <h4 className="font-semibold text-gray-900">{hotel.name}</h4>
                    <div className="text-right">
                      <div className="font-bold text-blue-600">${hotel.pricePerNight}</div>
                      <div className="text-xs text-gray-500">per night</div>
                    </div>
                  </div>
                  <div className="flex items-center mb-2">
                    <div className="flex items-center text-yellow-500 mr-2">
                      <i className="ri-star-fill text-sm"></i>
                      <span className="text-sm ml-1">{hotel.rating}</span>
                    </div>
                    <span className="text-xs text-gray-500">{hotel.location}</span>
                  </div>
                  <div className="flex flex-wrap gap-1 mb-2">
                    {hotel.amenities.slice(0, 3).map((amenity, i) => (
                      <span key={i} className="bg-gray-100 text-gray-700 px-2 py-1 rounded text-xs">
                        {amenity}
                      </span>
                    ))}
                  </div>
                  <p className="text-xs text-gray-600">Perfect for {hotel.suitableFor} travel</p>
                </div>
              ))}
            </div>
          </div>

          {/* Restaurant Suggestions */}
          <div className="bg-white rounded-xl shadow-lg p-6 mb-8">
            <h3 className="text-xl font-bold mb-4">
              <i className="ri-restaurant-line mr-2 text-green-600"></i>
              Restaurant Recommendations
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {itinerary.restaurants.map((restaurant, index) => (
                <div key={index} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                  <div className="flex justify-between items-start mb-2">
                    <h4 className="font-semibold text-gray-900">{restaurant.name}</h4>
                    <div className="text-right">
                      <div className="font-bold text-green-600">${restaurant.priceRange}</div>
                      <div className="text-xs text-gray-500">avg meal</div>
                    </div>
                  </div>
                  <div className="flex items-center mb-2">
                    <div className="flex items-center text-yellow-500 mr-2">
                      <i className="ri-star-fill text-sm"></i>
                      <span className="text-sm ml-1">{restaurant.rating}</span>
                    </div>
                    <span className="text-xs text-gray-500">{restaurant.atmosphere}</span>
                  </div>
                  <p className="text-sm text-gray-700 mb-2">{restaurant.cuisine}</p>
                  <div className="flex flex-wrap gap-1 mb-2">
                    {restaurant.dietaryFriendly.map((diet, i) => (
                      <span key={i} className="bg-green-100 text-green-700 px-2 py-1 rounded text-xs">
                        {diet}
                      </span>
                    ))}
                  </div>
                  <p className="text-xs text-gray-600">
                    Specialties: {restaurant.specialties.slice(0, 2).join(', ')}
                  </p>
                </div>
              ))}
            </div>
          </div>

          {/* Day-by-day Itinerary */}
          <div className="space-y-6 mb-8">
            <h3 className="text-2xl font-bold text-center text-gray-900 mb-6">
              <i className="ri-calendar-line mr-2 text-blue-600"></i>
              Day-by-Day Itinerary
            </h3>
            {itinerary.days.map((day) => (
              <div key={day.day} className="bg-white rounded-xl shadow-lg overflow-hidden">
                <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-4">
                  <div className="flex justify-between items-center">
                    <div>
                      <h3 className="text-xl font-bold">Day {day.day}: {day.title}</h3>
                      <p className="text-blue-100">{day.weather}</p>
                    </div>
                    <div className="text-right">
                      <div className="text-lg font-semibold">${day.totalCost}</div>
                      <div className="text-sm text-blue-100">daily budget</div>
                    </div>
                  </div>
                </div>
                <div className="p-6">
                  <div className="space-y-4">
                    {day.activities.map((activity, index) => (
                      <div key={index} className="flex items-start space-x-4 p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                        <div className="flex-shrink-0">
                          <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                            <i className={`${getActivityIcon(activity.type)} text-blue-600`}></i>
                          </div>
                        </div>
                        <div className="flex-1">
                          <div className="flex justify-between items-start mb-2">
                            <div>
                              <h4 className="font-semibold text-gray-900">{activity.title}</h4>
                              <p className="text-sm text-gray-600">{activity.time} • {activity.duration}</p>
                            </div>
                            <div className="text-right">
                              <div className="font-semibold text-gray-900">${activity.cost}</div>
                              <div className="text-xs text-gray-500 capitalize">{activity.type}</div>
                            </div>
                          </div>
                          <p className="text-gray-700 mb-2">{activity.description}</p>
                          {activity.tips && (
                            <div className="flex items-center text-sm text-blue-600 bg-blue-50 px-3 py-1 rounded-full">
                              <i className="ri-lightbulb-line mr-1"></i>
                              <span>{activity.tips}</span>
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                  {day.tips && (
                    <div className="mt-4 p-3 bg-yellow-50 border-l-4 border-yellow-400 rounded">
                      <p className="text-sm text-yellow-800">
                        <i className="ri-information-line mr-1"></i>
                        <strong>Day Tip:</strong> {day.tips}
                      </p>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>

          {/* Travel Tips */}
          {itinerary.travelTips && (
            <div className="bg-white rounded-xl shadow-lg p-6 mb-8">
              <h3 className="text-xl font-bold mb-4">
                <i className="ri-lightbulb-line mr-2 text-yellow-500"></i>
                Essential Travel Tips
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {itinerary.travelTips.map((tip, index) => (
                  <div key={index} className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
                    <i className={`${tip.icon} text-blue-600 text-lg mt-1`}></i>
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-1">{tip.category}</h4>
                      <p className="text-sm text-gray-600">{tip.tip}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="text-center space-x-4">
            <button
              onClick={() => {
                setItinerary(null);
                setStep(1);
                setFormData({
                  destination: '',
                  startDate: '',
                  endDate: '',
                  duration: '',
                  budget: '',
                  currency: 'USD',
                  companionType: '',
                  groupSize: 1,
                  activityPreferences: [],
                  dietaryOptions: [],
                  specialRequirements: '',
                  accommodationType: '',
                  transportPreference: ''
                });
              }}
              className="bg-gray-600 hover:bg-gray-700 text-white px-6 py-3 rounded-lg transition-colors"
            >
              Plan Another Trip
            </button>
            <button
              onClick={() => saveItinerary(itinerary)}
              className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg transition-colors"
            >
              <i className="ri-save-line mr-2"></i>
              Save Itinerary
            </button>
            <button
              onClick={() => downloadPDF(itinerary)}
              className="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg transition-colors"
            >
              <i className="ri-download-line mr-2"></i>
              Download PDF
            </button>
            <button
              onClick={() => shareItinerary(itinerary)}
              className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-lg transition-colors"
            >
              <i className="ri-share-line mr-2"></i>
              Share Plan
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 py-8 px-4">
      <div className="max-w-3xl mx-auto">
        {/* Progress Bar */}
        <div className="mb-8">
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm font-medium text-gray-700">Step {step} of 6</span>
            <span className="text-sm text-gray-500">{Math.round((step / 6) * 100)}% Complete</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-3">
            <div 
              className="bg-gradient-to-r from-blue-600 to-purple-600 h-3 rounded-full transition-all duration-500"
              style={{ width: `${(step / 6) * 100}%` }}
            ></div>
          </div>
        </div>

        <div className="bg-white rounded-2xl shadow-xl p-8">
          {/* Step 1: Destination */}
          {step === 1 && (
            <div className="text-center">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                Where would you like to go?
              </h2>
              <p className="text-gray-600 mb-8">
                Enter your dream destination to start planning your perfect trip
              </p>
              <div className="relative max-w-md mx-auto">
                <i className="ri-map-pin-line absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 text-xl"></i>
                <input
                  type="text"
                  placeholder="e.g., Paris, Tokyo, New York"
                  value={formData.destination}
                  onChange={(e) => handleInputChange('destination', e.target.value)}
                  className="w-full pl-12 pr-4 py-4 border-2 border-gray-200 rounded-xl text-lg focus:border-blue-500 focus:outline-none"
                />
              </div>
            </div>
          )}

          {/* Step 2: Dates & Budget */}
          {step === 2 && (
            <div className="text-center">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                When are you traveling?
              </h2>
              <p className="text-gray-600 mb-8">
                Select your travel dates and budget
              </p>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-2xl mx-auto">
                <div>
                  <label className="block text-left text-gray-700 font-medium mb-2">
                    Start Date
                  </label>
                  <input
                    type="date"
                    value={formData.startDate}
                    onChange={(e) => {
                      handleInputChange('startDate', e.target.value);
                      calculateDuration();
                    }}
                    className="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:border-blue-500 focus:outline-none"
                  />
                </div>
                <div>
                  <label className="block text-left text-gray-700 font-medium mb-2">
                    End Date
                  </label>
                  <input
                    type="date"
                    value={formData.endDate}
                    onChange={(e) => {
                      handleInputChange('endDate', e.target.value);
                      calculateDuration();
                    }}
                    className="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:border-blue-500 focus:outline-none"
                  />
                </div>
                <div>
                  <label className="block text-left text-gray-700 font-medium mb-2">
                    Total Budget
                  </label>
                  <div className="relative">
                    <span className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-500">$</span>
                    <input
                      type="number"
                      placeholder="2000"
                      value={formData.budget}
                      onChange={(e) => handleInputChange('budget', e.target.value)}
                      className="w-full pl-8 pr-4 py-3 border-2 border-gray-200 rounded-xl focus:border-blue-500 focus:outline-none"
                    />
                  </div>
                </div>
                <div>
                  <label className="block text-left text-gray-700 font-medium mb-2">
                    Duration
                  </label>
                  <input
                    type="text"
                    value={formData.duration ? `${formData.duration} days` : ''}
                    readOnly
                    placeholder="Auto-calculated"
                    className="w-full px-4 py-3 border-2 border-gray-200 rounded-xl bg-gray-50"
                  />
                </div>
              </div>
            </div>
          )}

          {/* Step 3: Companion Type */}
          {step === 3 && (
            <div className="text-center">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                Who are you traveling with?
              </h2>
              <p className="text-gray-600 mb-8">
                This helps us customize your experience
              </p>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-w-2xl mx-auto">
                {companionTypes.map((type) => (
                  <button
                    key={type.id}
                    onClick={() => handleInputChange('companionType', type.id)}
                    className={`p-4 rounded-xl border-2 transition-all text-left ${
                      formData.companionType === type.id
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <div className="flex items-center mb-2">
                      <span className="text-2xl mr-3">{type.icon}</span>
                      <span className="font-bold text-gray-900">{type.name}</span>
                    </div>
                    <p className="text-gray-600 text-sm">{type.description}</p>
                  </button>
                ))}
              </div>
              {formData.companionType && (
                <div className="mt-6 max-w-xs mx-auto">
                  <label className="block text-gray-700 font-medium mb-2">
                    Group Size
                  </label>
                  <select
                    value={formData.groupSize}
                    onChange={(e) => handleInputChange('groupSize', parseInt(e.target.value))}
                    className="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:border-blue-500 focus:outline-none"
                  >
                    {[1,2,3,4,5,6,7,8,9,10].map(num => (
                      <option key={num} value={num}>
                        {num} {num === 1 ? 'person' : 'people'}
                      </option>
                    ))}
                  </select>
                </div>
              )}
            </div>
          )}

          {/* Step 4: Activity Preferences */}
          {step === 4 && (
            <div className="text-center">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                What interests you most?
              </h2>
              <p className="text-gray-600 mb-8">
                Select all activities that appeal to you (multiple selections allowed)
              </p>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-3 max-w-4xl mx-auto">
                {activityOptions.map((activity) => (
                  <button
                    key={activity.id}
                    onClick={() => handleArrayToggle('activityPreferences', activity.id)}
                    className={`p-3 rounded-xl border-2 transition-all ${
                      formData.activityPreferences.includes(activity.id)
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <div className="text-2xl mb-1">{activity.icon}</div>
                    <div className="text-sm font-medium text-gray-900">{activity.name}</div>
                    <div className="text-xs text-gray-600 mt-1">{activity.description}</div>
                  </button>
                ))}
              </div>
              <p className="text-sm text-gray-500 mt-4">
                Selected: {formData.activityPreferences.length} activities
              </p>
            </div>
          )}

          {/* Step 5: Dietary Options */}
          {step === 5 && (
            <div className="text-center">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                Any dietary requirements?
              </h2>
              <p className="text-gray-600 mb-8">
                Help us recommend the right restaurants and food experiences
              </p>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-3 max-w-4xl mx-auto">
                {dietaryOptions.map((option) => (
                  <button
                    key={option.id}
                    onClick={() => handleArrayToggle('dietaryOptions', option.id)}
                    className={`p-3 rounded-xl border-2 transition-all ${
                      formData.dietaryOptions.includes(option.id)
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <div className="text-2xl mb-1">{option.icon}</div>
                    <div className="text-sm font-medium text-gray-900">{option.name}</div>
                  </button>
                ))}
              </div>
              <div className="mt-6 max-w-md mx-auto">
                <label className="block text-gray-700 font-medium mb-2">
                  Special Requirements (Optional)
                </label>
                <textarea
                  placeholder="Any allergies, accessibility needs, or special requests..."
                  value={formData.specialRequirements}
                  onChange={(e) => handleInputChange('specialRequirements', e.target.value)}
                  className="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:border-blue-500 focus:outline-none"
                  rows="3"
                />
              </div>
            </div>
          )}

          {/* Step 6: Accommodation & Transport */}
          {step === 6 && (
            <div className="text-center">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                Accommodation & Transport
              </h2>
              <p className="text-gray-600 mb-8">
                Choose your preferred accommodation and transport options
              </p>

              <div className="mb-8">
                <h3 className="text-lg font-semibold text-gray-800 mb-4">Accommodation Type</h3>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-3 max-w-3xl mx-auto">
                  {accommodationTypes.map((type) => (
                    <button
                      key={type.id}
                      onClick={() => handleInputChange('accommodationType', type.id)}
                      className={`p-3 rounded-xl border-2 transition-all ${
                        formData.accommodationType === type.id
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                    >
                      <div className="text-2xl mb-1">{type.icon}</div>
                      <div className="text-sm font-medium text-gray-900">{type.name}</div>
                    </button>
                  ))}
                </div>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-gray-800 mb-4">Transport Preference</h3>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-3 max-w-3xl mx-auto">
                  {transportOptions.map((option) => (
                    <button
                      key={option.id}
                      onClick={() => handleInputChange('transportPreference', option.id)}
                      className={`p-3 rounded-xl border-2 transition-all ${
                        formData.transportPreference === option.id
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                    >
                      <div className="text-2xl mb-1">{option.icon}</div>
                      <div className="text-sm font-medium text-gray-900">{option.name}</div>
                    </button>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* Navigation Buttons */}
          <div className="flex justify-between mt-8">
            <button
              onClick={prevStep}
              disabled={step === 1}
              className={`px-6 py-3 rounded-lg font-medium transition-colors ${
                step === 1 
                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed' 
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
            >
              Previous
            </button>
            <button
              onClick={nextStep}
              disabled={!canProceed()}
              className={`px-8 py-3 rounded-lg font-medium transition-colors ${
                canProceed()
                  ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white hover:from-blue-700 hover:to-purple-700'
                  : 'bg-gray-200 text-gray-400 cursor-not-allowed'
              }`}
            >
              {step === 6 ? 'Generate Itinerary' : 'Next'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ComprehensiveAIPlanner;
