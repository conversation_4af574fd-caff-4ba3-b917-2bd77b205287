
import { Link } from "react-router-dom";
import SearchBar from "../components/SearchBar";
import FeaturedTours from "../components/FeaturedTours";

const Home = () => {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <div className="bg-hero bg-opacity-100 w-full bg-cover min-h-[85vh] sm:min-h-[75vh] md:min-h-[80vh] lg:h-[28rem] object-cover bg-center relative">
        <div className="absolute inset-0 bg-black bg-opacity-30"></div>
        <div className="relative flex flex-col justify-center items-center h-full px-4 sm:px-6 lg:px-8" id="about">

          {/* Hero Text */}
          <div className="text-center max-w-4xl mx-auto mb-8 sm:mb-12">
            <h1 className="text-white font-shantell text-lg sm:text-xl md:text-2xl lg:text-3xl xl:text-4xl leading-relaxed mb-6 px-4">
              <span className="block mb-2 font-bold text-yellow-300">Travel Ease!</span>
              One-stop travel solution for all your tour and travel needs.
              <span className="block mt-2">Discover tour packages from all over the world with just a few clicks.</span>
              <span className="block mt-2 text-base sm:text-lg md:text-xl">
                So what are you waiting for? Start planning your dream vacation today!
              </span>
            </h1>
          </div>

          {/* Search Bar */}
          <div className="w-full max-w-6xl mx-auto mb-6 sm:mb-8">
            <SearchBar/>
          </div>

          {/* Smart Travel Planner CTA */}
          <div className="text-center">
            <Link
              to="/ai-travel-planner"
              className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-bold py-3 px-6 sm:py-4 sm:px-8 rounded-full shadow-lg transform hover:scale-105 transition-all duration-200 inline-flex items-center text-sm sm:text-base"
            >
              <i className="ri-magic-line mr-2 text-lg sm:text-xl"></i>
              <span className="hidden sm:inline">Create Smart Travel Plan with AI</span>
              <span className="sm:hidden">AI Travel Planner</span>
            </Link>
          </div>
        </div>
      </div>

      {/* Featured Tours Section */}
      <div className="py-8 sm:py-12 lg:py-16 px-4 sm:px-6 lg:px-8 bg-gray-50">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-8 sm:mb-12">
            <h2 className="text-2xl sm:text-3xl lg:text-4xl font-shantell text-gray-800 mb-4">
              <span className="bg-yellow-300 px-4 py-2 rounded-full font-bold">Explore</span>
              <span className="block mt-2 sm:inline sm:ml-2">Our Featured Tours</span>
            </h2>
          </div>
          <FeaturedTours />
        </div>
      </div>
    </div>
  );
};

export default Home;
