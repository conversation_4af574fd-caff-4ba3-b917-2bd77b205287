
import { Link } from "react-router-dom";

const Home = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Hero Section */}
      <div className="relative min-h-screen flex flex-col justify-center items-center px-4 sm:px-6 lg:px-8">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-600/10 via-purple-600/5 to-pink-600/10"></div>

        {/* Main Content */}
        <div className="relative z-10 text-center max-w-6xl mx-auto">
          {/* Hero Text */}
          <div className="mb-12">
            <h1 className="text-4xl sm:text-5xl lg:text-6xl xl:text-7xl font-bold text-gray-900 mb-6 leading-tight">
              <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                Craft Unforgettable
              </span>
              <br />
              <span className="text-gray-800">Itineraries with AI</span>
            </h1>
            <p className="text-lg sm:text-xl lg:text-2xl text-gray-600 mb-8 max-w-4xl mx-auto leading-relaxed">
              Your personal trip planner and travel curator, creating custom itineraries
              tailored to your interests and budget.
            </p>
            <p className="text-base sm:text-lg text-gray-500 mb-12">
              Get started—it's free
            </p>
          </div>

          {/* Main CTA Button */}
          <div className="mb-16">
            <Link
              to="/ai-travel-planner"
              className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-bold py-4 px-8 sm:py-5 sm:px-12 rounded-full shadow-xl transform hover:scale-105 transition-all duration-300 inline-flex items-center text-lg sm:text-xl"
            >
              <i className="ri-magic-line mr-3 text-2xl"></i>
              Start Planning with AI
            </Link>
          </div>

          {/* Feature Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
            {/* AI Travel Feature */}
            <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
              <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center mb-4 mx-auto">
                <i className="ri-robot-line text-white text-xl"></i>
              </div>
              <h3 className="text-lg font-bold text-gray-800 mb-2">AI Travel</h3>
              <p className="text-gray-600 text-sm">
                Generate richly personalized accommodation recommendations driven by your unique preferences and tastes.
              </p>
            </div>

            {/* Adjust Itinerary Feature */}
            <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
              <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-blue-500 rounded-full flex items-center justify-center mb-4 mx-auto">
                <i className="ri-edit-line text-white text-xl"></i>
              </div>
              <h3 className="text-lg font-bold text-gray-800 mb-2">Adjust Itinerary</h3>
              <p className="text-gray-600 text-sm">
                Seamlessly manage your itinerary - reconfigure, add new destinations, or modify plans as needed.
              </p>
            </div>

            {/* Offline Access Feature */}
            <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
              <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mb-4 mx-auto">
                <i className="ri-download-line text-white text-xl"></i>
              </div>
              <h3 className="text-lg font-bold text-gray-800 mb-2">Offline Access</h3>
              <p className="text-gray-600 text-sm">
                Download and save your plans as PDF. Always have your information at hand, no matter where you are.
              </p>
            </div>

            {/* Everything in One Space Feature */}
            <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
              <div className="w-12 h-12 bg-gradient-to-r from-pink-500 to-red-500 rounded-full flex items-center justify-center mb-4 mx-auto">
                <i className="ri-dashboard-line text-white text-xl"></i>
              </div>
              <h3 className="text-lg font-bold text-gray-800 mb-2">Everything in One Space</h3>
              <p className="text-gray-600 text-sm">
                Whether it's your personalized trip or bookmarked plan, find everything organized on a single page.
              </p>
            </div>
          </div>

          {/* Bottom CTA */}
          <div className="text-center">
            <p className="text-gray-600 mb-6 text-lg">
              Skip the manual trip planning and start your effortless journey with Trip Planner AI today, at no cost.
            </p>
            <Link
              to="/ai-travel-planner"
              className="bg-white text-blue-600 hover:text-blue-700 font-bold py-3 px-8 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 inline-flex items-center border-2 border-blue-600 hover:border-blue-700"
            >
              Try Now
              <i className="ri-arrow-right-line ml-2"></i>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Home;
