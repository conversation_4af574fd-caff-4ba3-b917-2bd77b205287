const mongoose = require('mongoose');

const itineraryItemSchema = new mongoose.Schema({
  day: {
    type: Number,
    required: true,
  },
  tourId: {
    type: mongoose.Types.ObjectId,
    ref: "tour",
    required: true,
  },
  startTime: {
    type: String, // e.g., "09:00"
    required: true,
  },
  endTime: {
    type: String, // e.g., "17:00"
    required: true,
  },
  notes: {
    type: String,
    default: "",
  },
  estimatedCost: {
    type: Number,
    required: true,
  }
});

const travelPlanSchema = new mongoose.Schema(
  {
    userId: {
      type: mongoose.Types.ObjectId,
      ref: "user",
      required: true,
    },
    planName: {
      type: String,
      required: true,
    },
    destinations: [{
      city: {
        type: String,
        required: true,
      },
      country: {
        type: String,
        required: true,
      },
      coordinates: {
        latitude: Number,
        longitude: Number,
      },
      arrivalDate: {
        type: Date,
        required: true,
      },
      departureDate: {
        type: Date,
        required: true,
      }
    }],
    totalBudget: {
      type: Number,
      required: true,
    },
    currentSpent: {
      type: Number,
      default: 0,
    },
    travelStyle: {
      type: String,
      enum: ['Budget', 'Mid-range', 'Luxury', 'Backpacker', 'Family', 'Business'],
      required: true,
    },
    preferences: {
      activities: [{
        type: String, // Adventure, Cultural, Relaxation, etc.
      }],
      accommodation: {
        type: String,
        enum: ['Hotel', 'Hostel', 'Apartment', 'Resort', 'Guesthouse'],
        default: 'Hotel',
      },
      transportation: {
        type: String,
        enum: ['Flight', 'Train', 'Bus', 'Car', 'Mixed'],
        default: 'Mixed',
      },
      groupSize: {
        type: Number,
        default: 1,
      },
      accessibility: {
        type: Boolean,
        default: false,
      }
    },
    itinerary: [itineraryItemSchema],
    aiRecommendations: [{
      type: {
        type: String,
        enum: ['tour', 'restaurant', 'activity', 'accommodation', 'transport'],
        required: true,
      },
      title: {
        type: String,
        required: true,
      },
      description: {
        type: String,
        required: true,
      },
      location: {
        type: String,
        required: true,
      },
      estimatedCost: {
        type: Number,
        required: true,
      },
      rating: {
        type: Number,
        min: 0,
        max: 5,
        default: 0,
      },
      reasonForRecommendation: {
        type: String,
        required: true,
      }
    }],
    status: {
      type: String,
      enum: ['Planning', 'Confirmed', 'In Progress', 'Completed', 'Cancelled'],
      default: 'Planning',
    },
    isPublic: {
      type: Boolean,
      default: false,
    },
    weatherAlerts: [{
      date: Date,
      location: String,
      alert: String,
      severity: {
        type: String,
        enum: ['Low', 'Medium', 'High'],
        default: 'Low',
      }
    }]
  },
  { timestamps: true }
);

// Index for efficient queries
travelPlanSchema.index({ userId: 1, status: 1 });
travelPlanSchema.index({ 'destinations.city': 1 });
travelPlanSchema.index({ travelStyle: 1, totalBudget: 1 });

module.exports = mongoose.model("travelPlan", travelPlanSchema);
