import React, { useState, useContext } from 'react';
import { AuthContext } from '../context/AuthContext';
import axios from 'axios';
import { useNavigate } from 'react-router-dom';

const TravelPlanner = () => {
  const { currentUser } = useContext(AuthContext);
  const navigate = useNavigate();
  
  const [formData, setFormData] = useState({
    planName: '',
    destinations: [{ city: '', country: '', arrivalDate: '', departureDate: '' }],
    totalBudget: '',
    travelStyle: 'Mid-range',
    preferences: {
      activities: [],
      accommodation: 'Hotel',
      transportation: 'Mixed',
      groupSize: 1,
      accessibility: false
    }
  });
  
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const activityOptions = [
    'Adventure', 'Cultural', 'Relaxation', 'Wildlife', 'Photography', 
    'Food & Drink', 'Shopping', 'Nightlife', 'Sports', 'Nature'
  ];

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    
    if (name.includes('preferences.')) {
      const prefKey = name.split('.')[1];
      setFormData(prev => ({
        ...prev,
        preferences: {
          ...prev.preferences,
          [prefKey]: type === 'checkbox' ? checked : value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: type === 'checkbox' ? checked : value
      }));
    }
  };

  const handleDestinationChange = (index, field, value) => {
    const updatedDestinations = [...formData.destinations];
    updatedDestinations[index][field] = value;
    setFormData(prev => ({
      ...prev,
      destinations: updatedDestinations
    }));
  };

  const addDestination = () => {
    setFormData(prev => ({
      ...prev,
      destinations: [...prev.destinations, { city: '', country: '', arrivalDate: '', departureDate: '' }]
    }));
  };

  const removeDestination = (index) => {
    if (formData.destinations.length > 1) {
      const updatedDestinations = formData.destinations.filter((_, i) => i !== index);
      setFormData(prev => ({
        ...prev,
        destinations: updatedDestinations
      }));
    }
  };

  const handleActivityToggle = (activity) => {
    const currentActivities = formData.preferences.activities;
    const updatedActivities = currentActivities.includes(activity)
      ? currentActivities.filter(a => a !== activity)
      : [...currentActivities, activity];
    
    setFormData(prev => ({
      ...prev,
      preferences: {
        ...prev.preferences,
        activities: updatedActivities
      }
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!currentUser) {
      setError('Please login to create a travel plan');
      return;
    }

    setLoading(true);
    setError('');

    try {
      const planData = {
        ...formData,
        userId: currentUser.data._id,
        totalBudget: parseFloat(formData.totalBudget),
        preferences: {
          ...formData.preferences,
          groupSize: parseInt(formData.preferences.groupSize)
        }
      };

      const response = await axios.post(
        'https://traveltrek.onrender.com/travel-planner/create',
        planData,
        {
          headers: {
            'Content-Type': 'application/json'
          }
        }
      );

      if (response.data.success) {
        navigate(`/travel-plan/${response.data.data._id}`);
      }
    } catch (error) {
      setError('Error creating travel plan. Please try again.');
      console.error('Error:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6 bg-white shadow-lg rounded-lg font-shantell">
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-gray-800 mb-2">
          <i className="ri-map-2-line text-blue-600 mr-2"></i>
          Smart Travel Planner
        </h1>
        <p className="text-gray-600">Create your perfect personalized itinerary with AI recommendations</p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Plan Name */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Plan Name *
          </label>
          <input
            type="text"
            name="planName"
            value={formData.planName}
            onChange={handleInputChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="e.g., Summer Europe Trip 2024"
            required
          />
        </div>

        {/* Destinations */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Destinations *
          </label>
          {formData.destinations.map((destination, index) => (
            <div key={index} className="border border-gray-200 rounded-md p-4 mb-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <input
                  type="text"
                  placeholder="City"
                  value={destination.city}
                  onChange={(e) => handleDestinationChange(index, 'city', e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                />
                <input
                  type="text"
                  placeholder="Country"
                  value={destination.country}
                  onChange={(e) => handleDestinationChange(index, 'country', e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                />
                <input
                  type="date"
                  value={destination.arrivalDate}
                  onChange={(e) => handleDestinationChange(index, 'arrivalDate', e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                />
                <input
                  type="date"
                  value={destination.departureDate}
                  onChange={(e) => handleDestinationChange(index, 'departureDate', e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                />
              </div>
              {formData.destinations.length > 1 && (
                <button
                  type="button"
                  onClick={() => removeDestination(index)}
                  className="mt-2 text-red-600 hover:text-red-800 text-sm"
                >
                  <i className="ri-delete-bin-line mr-1"></i>
                  Remove Destination
                </button>
              )}
            </div>
          ))}
          <button
            type="button"
            onClick={addDestination}
            className="text-blue-600 hover:text-blue-800 text-sm font-medium"
          >
            <i className="ri-add-line mr-1"></i>
            Add Another Destination
          </button>
        </div>

        {/* Budget and Travel Style */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Total Budget (USD) *
            </label>
            <input
              type="number"
              name="totalBudget"
              value={formData.totalBudget}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="e.g., 2000"
              min="0"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Travel Style *
            </label>
            <select
              name="travelStyle"
              value={formData.travelStyle}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              required
            >
              <option value="Budget">Budget</option>
              <option value="Mid-range">Mid-range</option>
              <option value="Luxury">Luxury</option>
              <option value="Backpacker">Backpacker</option>
              <option value="Family">Family</option>
              <option value="Business">Business</option>
            </select>
          </div>
        </div>

        {/* Activities */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Preferred Activities
          </label>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-2">
            {activityOptions.map(activity => (
              <label key={activity} className="flex items-center space-x-2 cursor-pointer">
                <input
                  type="checkbox"
                  checked={formData.preferences.activities.includes(activity)}
                  onChange={() => handleActivityToggle(activity)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="text-sm text-gray-700">{activity}</span>
              </label>
            ))}
          </div>
        </div>

        {/* Additional Preferences */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Accommodation
            </label>
            <select
              name="preferences.accommodation"
              value={formData.preferences.accommodation}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="Hotel">Hotel</option>
              <option value="Hostel">Hostel</option>
              <option value="Apartment">Apartment</option>
              <option value="Resort">Resort</option>
              <option value="Guesthouse">Guesthouse</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Transportation
            </label>
            <select
              name="preferences.transportation"
              value={formData.preferences.transportation}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="Flight">Flight</option>
              <option value="Train">Train</option>
              <option value="Bus">Bus</option>
              <option value="Car">Car</option>
              <option value="Mixed">Mixed</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Group Size
            </label>
            <input
              type="number"
              name="preferences.groupSize"
              value={formData.preferences.groupSize}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              min="1"
              max="20"
            />
          </div>
        </div>

        {/* Accessibility */}
        <div>
          <label className="flex items-center space-x-2 cursor-pointer">
            <input
              type="checkbox"
              name="preferences.accessibility"
              checked={formData.preferences.accessibility}
              onChange={handleInputChange}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span className="text-sm text-gray-700">Accessibility requirements</span>
          </label>
        </div>

        {/* Error Message */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-md p-4">
            <p className="text-red-600 text-sm">{error}</p>
          </div>
        )}

        {/* Submit Button */}
        <div className="text-center">
          <button
            type="submit"
            disabled={loading}
            className="bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white font-medium py-3 px-8 rounded-md transition duration-200 flex items-center justify-center mx-auto"
          >
            {loading ? (
              <>
                <i className="ri-loader-4-line animate-spin mr-2"></i>
                Creating Your Plan...
              </>
            ) : (
              <>
                <i className="ri-magic-line mr-2"></i>
                Create Smart Travel Plan
              </>
            )}
          </button>
        </div>
      </form>
    </div>
  );
};

export default TravelPlanner;
