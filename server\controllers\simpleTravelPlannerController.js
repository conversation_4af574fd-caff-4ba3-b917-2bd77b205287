const SimpleTravelPlan = require('../models/simpleTravelPlanModel');
const Tour = require('../models/toursModel');

// Simple AI recommendation function
const generateSimpleRecommendations = (destination, budget, travelStyle, activities) => {
  const recommendations = [];
  
  // Budget-based recommendations
  if (budget < 500) {
    recommendations.push({
      title: `Budget Stay in ${destination}`,
      description: 'Affordable accommodation with good reviews',
      type: 'accommodation',
      estimatedCost: 50
    });
    recommendations.push({
      title: `Local Food Tour in ${destination}`,
      description: 'Taste authentic local cuisine at budget-friendly spots',
      type: 'food',
      estimatedCost: 30
    });
  } else if (budget > 1000) {
    recommendations.push({
      title: `Luxury Hotel in ${destination}`,
      description: 'Premium accommodation with world-class amenities',
      type: 'accommodation',
      estimatedCost: 200
    });
    recommendations.push({
      title: `Fine Dining Experience in ${destination}`,
      description: 'Michelin-starred restaurants and gourmet experiences',
      type: 'food',
      estimatedCost: 150
    });
  }

  // Activity-based recommendations
  activities.forEach(activity => {
    switch (activity) {
      case 'Adventure':
        recommendations.push({
          title: `Adventure Sports in ${destination}`,
          description: 'Thrilling outdoor activities and extreme sports',
          type: 'activity',
          estimatedCost: 80
        });
        break;
      case 'Cultural':
        recommendations.push({
          title: `Cultural Heritage Tour in ${destination}`,
          description: 'Explore museums, historical sites, and local culture',
          type: 'tour',
          estimatedCost: 45
        });
        break;
      case 'Food':
        recommendations.push({
          title: `Food Walking Tour in ${destination}`,
          description: 'Discover the best local restaurants and street food',
          type: 'food',
          estimatedCost: 60
        });
        break;
      case 'Nature':
        recommendations.push({
          title: `Nature Exploration in ${destination}`,
          description: 'Visit parks, gardens, and natural scenic spots',
          type: 'nature',
          estimatedCost: 35
        });
        break;
    }
  });

  // Travel style recommendations
  if (travelStyle === 'Family') {
    recommendations.push({
      title: `Family Fun Activities in ${destination}`,
      description: 'Kid-friendly attractions and family entertainment',
      type: 'activity',
      estimatedCost: 70
    });
  }

  return recommendations.slice(0, 6); // Return top 6 recommendations
};

// Create travel plan
const createTravelPlan = async (req, res) => {
  try {
    const { planName, destination, startDate, endDate, budget, groupSize, travelStyle, activities } = req.body;

    // Get user ID from token
    let userId;
    const authHeader = req.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.substring(7);
      try {
        const jwt = require('jsonwebtoken');
        const decoded = jwt.verify(token, process.env.JWT_SECRET_KEY);
        userId = decoded.id;
      } catch (error) {
        return res.status(401).json({
          success: false,
          message: 'Invalid token'
        });
      }
    } else {
      return res.status(401).json({
        success: false,
        message: 'No token provided'
      });
    }

    // Validate required fields
    if (!planName || !destination || !startDate || !endDate || !budget || !travelStyle) {
      return res.status(400).json({
        success: false,
        message: 'Please fill in all required fields'
      });
    }

    // Generate AI recommendations
    const recommendations = generateSimpleRecommendations(destination, budget, travelStyle, activities || []);

    const newTravelPlan = new SimpleTravelPlan({
      userId,
      planName,
      destination,
      startDate: new Date(startDate),
      endDate: new Date(endDate),
      budget,
      groupSize: groupSize || 1,
      travelStyle,
      activities: activities || [],
      recommendations
    });

    const savedPlan = await newTravelPlan.save();

    res.status(201).json({
      success: true,
      message: 'Travel plan created successfully',
      data: savedPlan
    });
  } catch (error) {
    console.error('Error creating travel plan:', error);
    res.status(500).json({
      success: false,
      message: 'Error creating travel plan',
      error: error.message
    });
  }
};

// Get user's travel plans
const getUserTravelPlans = async (req, res) => {
  try {
    const { userId } = req.params;
    
    const travelPlans = await SimpleTravelPlan.find({ userId }).sort({ createdAt: -1 });
    
    res.status(200).json({
      success: true,
      data: travelPlans
    });
  } catch (error) {
    console.error('Error fetching travel plans:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching travel plans',
      error: error.message
    });
  }
};

// Get single travel plan
const getTravelPlan = async (req, res) => {
  try {
    const { planId } = req.params;
    
    const travelPlan = await SimpleTravelPlan.findById(planId);
    
    if (!travelPlan) {
      return res.status(404).json({
        success: false,
        message: 'Travel plan not found'
      });
    }
    
    res.status(200).json({
      success: true,
      data: travelPlan
    });
  } catch (error) {
    console.error('Error fetching travel plan:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching travel plan',
      error: error.message
    });
  }
};

// Update travel plan
const updateTravelPlan = async (req, res) => {
  try {
    const { planId } = req.params;
    const updateData = req.body;

    // Get user ID from token
    let userId;
    const authHeader = req.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.substring(7);
      try {
        const jwt = require('jsonwebtoken');
        const decoded = jwt.verify(token, process.env.JWT_SECRET_KEY);
        userId = decoded.id;
      } catch (error) {
        return res.status(401).json({
          success: false,
          message: 'Invalid token'
        });
      }
    } else {
      return res.status(401).json({
        success: false,
        message: 'No token provided'
      });
    }
    
    const updatedPlan = await SimpleTravelPlan.findByIdAndUpdate(
      planId,
      updateData,
      { new: true, runValidators: true }
    );
    
    if (!updatedPlan) {
      return res.status(404).json({
        success: false,
        message: 'Travel plan not found'
      });
    }
    
    res.status(200).json({
      success: true,
      message: 'Travel plan updated successfully',
      data: updatedPlan
    });
  } catch (error) {
    console.error('Error updating travel plan:', error);
    res.status(500).json({
      success: false,
      message: 'Error updating travel plan',
      error: error.message
    });
  }
};

// Delete travel plan
const deleteTravelPlan = async (req, res) => {
  try {
    const { planId } = req.params;

    // Get user ID from token
    let userId;
    const authHeader = req.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.substring(7);
      try {
        const jwt = require('jsonwebtoken');
        const decoded = jwt.verify(token, process.env.JWT_SECRET_KEY);
        userId = decoded.id;
      } catch (error) {
        return res.status(401).json({
          success: false,
          message: 'Invalid token'
        });
      }
    } else {
      return res.status(401).json({
        success: false,
        message: 'No token provided'
      });
    }
    
    const deletedPlan = await SimpleTravelPlan.findByIdAndDelete(planId);
    
    if (!deletedPlan) {
      return res.status(404).json({
        success: false,
        message: 'Travel plan not found'
      });
    }
    
    res.status(200).json({
      success: true,
      message: 'Travel plan deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting travel plan:', error);
    res.status(500).json({
      success: false,
      message: 'Error deleting travel plan',
      error: error.message
    });
  }
};

module.exports = {
  createTravelPlan,
  getUserTravelPlans,
  getTravelPlan,
  updateTravelPlan,
  deleteTravelPlan
};
