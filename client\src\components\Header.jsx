import { useState, useContext } from "react";
import { Link } from "react-router-dom";
import logo from "../assets/images/logo.png";
import { AuthContext } from "../context/AuthContext";


const Header = () => {
  const [isOpen, setIsOpen] = useState(false);
   
   const {currentUser,logout}=useContext(AuthContext);


  return (
    <>
      <nav className="bg-white/95 backdrop-blur-sm border-b border-gray-100 px-4 sm:px-6 lg:px-8 sticky top-0 z-50 shadow-sm">
        <div className="max-w-7xl mx-auto flex items-center justify-between h-16">
          <Link to="/home" className="flex items-center space-x-2">
            <img src={logo} className="h-12 w-50.5"  alt="TravelTrek" />
            {/* <span className="font-poppins font-bold text-xl text-gray-900 hidden sm:block">TravelEase</span> */}
          </Link>
          <button
            onClick={() => setIsOpen(!isOpen)}
            type="button"
            className="md:hidden inline-flex items-center justify-center p-2 rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-pink-500"
          >
            <span className="sr-only">Open main menu</span>
            {isOpen ? (
              <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            ) : (
              <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            )}
          </button>
          <div className={`${isOpen ? "block" : "hidden"} md:block`}>
            <div className="absolute top-16 left-0 right-0 md:relative md:top-0 bg-white md:bg-transparent border-t md:border-t-0 border-gray-100 md:border-0 shadow-lg md:shadow-none">
              <ul className="flex flex-col md:flex-row md:space-x-1 p-4 md:p-0 space-y-2 md:space-y-0">
                <li>
                  <Link
                    to="/home"
                    className="flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-700 hover:text-pink-600 hover:bg-pink-50 transition-colors duration-200"
                  >
                    <i className="ri-home-4-line text-lg"></i>
                    <span>Home</span>
                  </Link>
                </li>

                <li>
                  <Link
                    to="/ai-travel-planner"
                    className="flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-700 hover:text-pink-600 hover:bg-pink-50 transition-colors duration-200"
                  >
                    <i className="ri-magic-line text-lg"></i>
                    <span>AI Planner</span>
                  </Link>
                </li>

                {currentUser ? (
                  <>
                    <li>
                      <Link
                        to="/ai-travel-plans"
                        className="flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-700 hover:text-pink-600 hover:bg-pink-50 transition-colors duration-200"
                      >
                        <i className="ri-list-check-line text-lg"></i>
                        <span>My Plans</span>
                      </Link>
                    </li>
                    <li>
                      <Link
                        to="/profile"
                        className="flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-700 hover:text-pink-600 hover:bg-pink-50 transition-colors duration-200"
                      >
                        <i className="ri-user-2-fill text-lg"></i>
                        <span className="hidden sm:block">{currentUser.data.username}</span>
                        <span className="sm:hidden">Profile</span>
                      </Link>
                    </li>
                    <li>
                      <button
                        onClick={logout}
                        className="flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-pink-500 to-purple-600 text-white rounded-lg hover:from-pink-600 hover:to-purple-700 transition-all duration-200 shadow-md hover:shadow-lg"
                      >
                        <span>Logout</span>
                        <i className="ri-logout-circle-r-line text-lg"></i>
                      </button>
                    </li>
                  </>
                ) : (
                  <>
                    <li>
                      <Link
                        to="/login"
                        className="flex items-center space-x-2 px-4 py-2 text-gray-700 hover:text-pink-600 transition-colors duration-200"
                      >
                        <i className="ri-login-box-line text-lg"></i>
                        <span>Login</span>
                      </Link>
                    </li>
                    <li>
                      <Link
                        to="/register"
                        className="flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-pink-500 to-purple-600 text-white rounded-lg hover:from-pink-600 hover:to-purple-700 transition-all duration-200 shadow-md hover:shadow-lg"
                      >
                        <i className="ri-login-box-fill text-lg"></i>
                        <span>Register</span>
                      </Link>
                    </li>
                  </>
                )}
              </ul>
            </div>
          </div>
        </div>
      </nav>
    </>
  );
};

export default Header;
