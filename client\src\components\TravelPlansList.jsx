import React, { useContext } from 'react';
import { Link } from 'react-router-dom';
import { AuthContext } from '../context/AuthContext';
import useFetch from '../hooks/useFetch';

const TravelPlansList = () => {
  const { currentUser } = useContext(AuthContext);
  
  const { data: travelPlans, loading } = useFetch(
    currentUser ? `https://traveltrek.onrender.com/travel-planner/user/${currentUser.data._id}` : null
  );

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'Planning': return 'bg-yellow-100 text-yellow-800';
      case 'Confirmed': return 'bg-green-100 text-green-800';
      case 'In Progress': return 'bg-blue-100 text-blue-800';
      case 'Completed': return 'bg-gray-100 text-gray-800';
      case 'Cancelled': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'Planning': return 'ri-draft-line';
      case 'Confirmed': return 'ri-check-line';
      case 'In Progress': return 'ri-time-line';
      case 'Completed': return 'ri-check-double-line';
      case 'Cancelled': return 'ri-close-line';
      default: return 'ri-question-line';
    }
  };

  if (!currentUser) {
    return (
      <div className="text-center py-12">
        <i className="ri-user-line text-6xl text-gray-400 mb-4"></i>
        <h2 className="text-2xl font-bold text-gray-800 mb-2">Please Login</h2>
        <p className="text-gray-600 mb-4">You need to login to view your travel plans.</p>
        <Link
          to="/login"
          className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md inline-block"
        >
          Login
        </Link>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="text-center">
          <i className="ri-loader-4-line text-4xl text-blue-600 animate-spin mb-4"></i>
          <p className="text-gray-600">Loading your travel plans...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto p-6 font-shantell">
      {/* Header */}
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-800 mb-2">
            <i className="ri-map-2-line text-blue-600 mr-2"></i>
            My Travel Plans
          </h1>
          <p className="text-gray-600">Manage and track all your travel adventures</p>
        </div>
        <Link
          to="/travel-planner"
          className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-md font-medium flex items-center"
        >
          <i className="ri-add-line mr-2"></i>
          Create New Plan
        </Link>
      </div>

      {/* Travel Plans Grid */}
      {travelPlans && travelPlans.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {travelPlans.map((plan) => (
            <div key={plan._id} className="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300">
              {/* Plan Header */}
              <div className="p-6">
                <div className="flex justify-between items-start mb-4">
                  <h3 className="text-xl font-bold text-gray-800 line-clamp-2">
                    {plan.planName}
                  </h3>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(plan.status)}`}>
                    <i className={`${getStatusIcon(plan.status)} mr-1`}></i>
                    {plan.status}
                  </span>
                </div>

                {/* Destinations */}
                <div className="mb-4">
                  <p className="text-gray-600 text-sm mb-2">
                    <i className="ri-map-pin-line mr-1"></i>
                    Destinations
                  </p>
                  <div className="flex flex-wrap gap-1">
                    {plan.destinations?.slice(0, 3).map((dest, index) => (
                      <span key={index} className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs">
                        {dest.city}
                      </span>
                    ))}
                    {plan.destinations?.length > 3 && (
                      <span className="bg-gray-100 text-gray-600 px-2 py-1 rounded-full text-xs">
                        +{plan.destinations.length - 3} more
                      </span>
                    )}
                  </div>
                </div>

                {/* Travel Dates */}
                <div className="mb-4">
                  <p className="text-gray-600 text-sm mb-1">
                    <i className="ri-calendar-line mr-1"></i>
                    Travel Dates
                  </p>
                  <p className="text-gray-800 text-sm">
                    {plan.destinations?.[0]?.arrivalDate && formatDate(plan.destinations[0].arrivalDate)} - 
                    {plan.destinations?.[plan.destinations.length - 1]?.departureDate && 
                     formatDate(plan.destinations[plan.destinations.length - 1].departureDate)}
                  </p>
                </div>

                {/* Budget Info */}
                <div className="mb-4">
                  <div className="flex justify-between items-center mb-2">
                    <p className="text-gray-600 text-sm">
                      <i className="ri-money-dollar-circle-line mr-1"></i>
                      Budget
                    </p>
                    <p className="text-gray-800 font-semibold text-sm">
                      {formatCurrency(plan.totalBudget)}
                    </p>
                  </div>
                  
                  {/* Budget Progress */}
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                      style={{
                        width: `${Math.min(((plan.currentSpent || 0) / plan.totalBudget) * 100, 100)}%`
                      }}
                    ></div>
                  </div>
                  <div className="flex justify-between text-xs text-gray-500 mt-1">
                    <span>Spent: {formatCurrency(plan.currentSpent || 0)}</span>
                    <span>
                      {Math.round(((plan.currentSpent || 0) / plan.totalBudget) * 100)}%
                    </span>
                  </div>
                </div>

                {/* Travel Style & Group Size */}
                <div className="flex justify-between items-center mb-4 text-sm text-gray-600">
                  <span>
                    <i className="ri-user-line mr-1"></i>
                    {plan.preferences?.groupSize} {plan.preferences?.groupSize === 1 ? 'Person' : 'People'}
                  </span>
                  <span className="bg-gray-100 px-2 py-1 rounded-full text-xs">
                    {plan.travelStyle}
                  </span>
                </div>

                {/* AI Recommendations Count */}
                {plan.aiRecommendations?.length > 0 && (
                  <div className="mb-4">
                    <p className="text-sm text-green-600">
                      <i className="ri-lightbulb-line mr-1"></i>
                      {plan.aiRecommendations.length} AI recommendations available
                    </p>
                  </div>
                )}

                {/* Action Buttons */}
                <div className="flex gap-2">
                  <Link
                    to={`/travel-plan/${plan._id}`}
                    className="flex-1 bg-blue-600 hover:bg-blue-700 text-white text-center py-2 px-4 rounded-md text-sm font-medium transition-colors"
                  >
                    View Details
                  </Link>
                  <button className="bg-gray-100 hover:bg-gray-200 text-gray-700 px-3 py-2 rounded-md text-sm">
                    <i className="ri-share-line"></i>
                  </button>
                </div>
              </div>

              {/* Plan Footer */}
              <div className="bg-gray-50 px-6 py-3 rounded-b-lg">
                <p className="text-xs text-gray-500">
                  Created {formatDate(plan.createdAt)}
                </p>
              </div>
            </div>
          ))}
        </div>
      ) : (
        /* Empty State */
        <div className="text-center py-12">
          <div className="bg-gray-100 rounded-full w-24 h-24 flex items-center justify-center mx-auto mb-6">
            <i className="ri-map-2-line text-4xl text-gray-400"></i>
          </div>
          <h2 className="text-2xl font-bold text-gray-800 mb-2">No Travel Plans Yet</h2>
          <p className="text-gray-600 mb-6 max-w-md mx-auto">
            Start planning your next adventure with our AI-powered travel planner. 
            Get personalized recommendations and create the perfect itinerary.
          </p>
          <Link
            to="/travel-planner"
            className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-md font-medium inline-flex items-center"
          >
            <i className="ri-magic-line mr-2"></i>
            Create Your First Plan
          </Link>
        </div>
      )}

      {/* Quick Stats */}
      {travelPlans && travelPlans.length > 0 && (
        <div className="mt-12 grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-white rounded-lg shadow-md p-4 text-center">
            <i className="ri-map-2-line text-2xl text-blue-600 mb-2"></i>
            <p className="text-2xl font-bold text-gray-800">{travelPlans.length}</p>
            <p className="text-sm text-gray-600">Total Plans</p>
          </div>
          <div className="bg-white rounded-lg shadow-md p-4 text-center">
            <i className="ri-check-line text-2xl text-green-600 mb-2"></i>
            <p className="text-2xl font-bold text-gray-800">
              {travelPlans.filter(plan => plan.status === 'Completed').length}
            </p>
            <p className="text-sm text-gray-600">Completed</p>
          </div>
          <div className="bg-white rounded-lg shadow-md p-4 text-center">
            <i className="ri-time-line text-2xl text-yellow-600 mb-2"></i>
            <p className="text-2xl font-bold text-gray-800">
              {travelPlans.filter(plan => plan.status === 'In Progress').length}
            </p>
            <p className="text-sm text-gray-600">In Progress</p>
          </div>
          <div className="bg-white rounded-lg shadow-md p-4 text-center">
            <i className="ri-money-dollar-circle-line text-2xl text-purple-600 mb-2"></i>
            <p className="text-2xl font-bold text-gray-800">
              {formatCurrency(travelPlans.reduce((total, plan) => total + plan.totalBudget, 0))}
            </p>
            <p className="text-sm text-gray-600">Total Budget</p>
          </div>
        </div>
      )}
    </div>
  );
};

export default TravelPlansList;
