const express = require('express');
const {
  createTravelPlan,
  getUserTravelPlans,
  getTravelPlan,
  updateTravelPlan,
  addToItinerary,
  generateOptimizedRoute
} = require('../controllers/travelPlannerController');

const router = express.Router();

// Create a new travel plan
router.post('/create', createTravelPlan);

// Get all travel plans for a user
router.get('/user/:userId', getUserTravelPlans);

// Get a specific travel plan
router.get('/:planId', getTravelPlan);

// Update a travel plan
router.put('/:planId', updateTravelPlan);

// Add tour to itinerary
router.post('/:planId/itinerary', addToItinerary);

// Generate optimized route
router.post('/:planId/optimize', generateOptimizedRoute);

module.exports = router;
