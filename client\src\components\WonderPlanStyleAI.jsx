import React, { useState, useContext } from 'react';
import { useNavigate } from 'react-router-dom';
import { AuthContext } from '../context/AuthContext';

const WonderPlanStyleAI = () => {
  const { currentUser } = useContext(AuthContext);
  const navigate = useNavigate();
  
  const [step, setStep] = useState(1);
  const [formData, setFormData] = useState({
    destination: '',
    duration: '',
    budget: '',
    groupSize: 1,
    travelStyle: '',
    interests: []
  });
  
  const [loading, setLoading] = useState(false);
  const [recommendations, setRecommendations] = useState([]);
  const [showResults, setShowResults] = useState(false);

  const travelStyles = [
    { id: 'budget', name: 'Budget', icon: '💰', description: 'Affordable options without compromising quality' },
    { id: 'mid-range', name: 'Mid-Range', icon: '⭐', description: 'Perfect balance of comfort and value' },
    { id: 'luxury', name: 'Luxury', icon: '👑', description: 'Premium experiences and accommodations' },
    { id: 'adventure', name: 'Adventure', icon: '🏔️', description: 'Thrilling activities and outdoor experiences' },
    { id: 'family', name: 'Family', icon: '👨‍👩‍👧‍👦', description: 'Family-friendly activities and accommodations' }
  ];

  const interests = [
    { id: 'culture', name: 'Culture & History', icon: '🏛️' },
    { id: 'food', name: 'Food & Dining', icon: '🍽️' },
    { id: 'nature', name: 'Nature & Outdoors', icon: '🌿' },
    { id: 'adventure', name: 'Adventure Sports', icon: '🚀' },
    { id: 'relaxation', name: 'Relaxation & Spa', icon: '🧘‍♀️' },
    { id: 'nightlife', name: 'Nightlife & Entertainment', icon: '🎭' },
    { id: 'shopping', name: 'Shopping', icon: '🛍️' },
    { id: 'photography', name: 'Photography', icon: '📸' }
  ];

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleInterestToggle = (interestId) => {
    setFormData(prev => ({
      ...prev,
      interests: prev.interests.includes(interestId)
        ? prev.interests.filter(id => id !== interestId)
        : [...prev.interests, interestId]
    }));
  };

  const generateAIRecommendations = async () => {
    setLoading(true);
    
    try {
      // Simulate AI processing
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const mockRecommendations = [
        {
          id: 1,
          title: `Perfect ${formData.travelStyle} Stay in ${formData.destination}`,
          description: `Handpicked accommodations that match your ${formData.travelStyle} travel style and preferences.`,
          type: 'accommodation',
          estimatedCost: Math.floor(formData.budget * 0.4),
          duration: `${formData.duration} nights`,
          rating: 4.6,
          features: ['Free WiFi', 'Breakfast included', 'Central location']
        },
        {
          id: 2,
          title: `${formData.destination} Cultural Experience`,
          description: `Immerse yourself in local culture with guided tours and authentic experiences.`,
          type: 'cultural',
          estimatedCost: Math.floor(formData.budget * 0.2),
          duration: 'Multiple days',
          rating: 4.8,
          features: ['Expert guides', 'Small groups', 'Local insights']
        },
        {
          id: 3,
          title: `Local Cuisine & Food Tours`,
          description: `Discover authentic flavors and hidden culinary gems in ${formData.destination}.`,
          type: 'food',
          estimatedCost: Math.floor(formData.budget * 0.15),
          duration: 'Various meals',
          rating: 4.7,
          features: ['Local restaurants', 'Food tours', 'Cooking classes']
        },
        {
          id: 4,
          title: `Transportation & Getting Around`,
          description: `Complete transportation solution including airport transfers and local transport.`,
          type: 'transport',
          estimatedCost: Math.floor(formData.budget * 0.1),
          duration: 'Throughout trip',
          rating: 4.5,
          features: ['Airport transfer', 'Public transport', '24/7 support']
        }
      ];
      
      setRecommendations(mockRecommendations);
      setShowResults(true);
    } catch (error) {
      console.error('Error generating recommendations:', error);
    } finally {
      setLoading(false);
    }
  };

  const nextStep = () => {
    if (step < 4) {
      setStep(step + 1);
    } else {
      generateAIRecommendations();
    }
  };

  const prevStep = () => {
    if (step > 1) {
      setStep(step - 1);
    }
  };

  const canProceed = () => {
    switch (step) {
      case 1: return formData.destination.trim() !== '';
      case 2: return formData.duration !== '' && formData.budget !== '';
      case 3: return formData.travelStyle !== '';
      case 4: return formData.interests.length > 0;
      default: return false;
    }
  };

  if (showResults) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 py-8 px-4">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              Your Personalized Travel Plan
            </h1>
            <p className="text-gray-600">
              AI-curated recommendations for {formData.destination}
            </p>
          </div>

          <div className="grid gap-6">
            {recommendations.map((rec) => (
              <div key={rec.id} className="bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-shadow">
                <div className="flex justify-between items-start mb-4">
                  <div>
                    <h3 className="text-xl font-bold text-gray-900 mb-2">{rec.title}</h3>
                    <p className="text-gray-600 mb-3">{rec.description}</p>
                    <div className="flex items-center space-x-4 text-sm text-gray-500">
                      <span className="flex items-center">
                        <i className="ri-time-line mr-1"></i>
                        {rec.duration}
                      </span>
                      <span className="flex items-center">
                        <i className="ri-star-fill mr-1 text-yellow-500"></i>
                        {rec.rating}
                      </span>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-2xl font-bold text-blue-600">
                      ${rec.estimatedCost}
                    </div>
                    <div className="text-sm text-gray-500">estimated</div>
                  </div>
                </div>
                
                <div className="flex flex-wrap gap-2 mb-4">
                  {rec.features.map((feature, index) => (
                    <span key={index} className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm">
                      {feature}
                    </span>
                  ))}
                </div>
                
                <div className="flex justify-between items-center">
                  <span className="bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm capitalize">
                    {rec.type}
                  </span>
                  <button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors">
                    Add to Plan
                  </button>
                </div>
              </div>
            ))}
          </div>

          <div className="text-center mt-8">
            <button
              onClick={() => {
                setShowResults(false);
                setStep(1);
                setFormData({
                  destination: '',
                  duration: '',
                  budget: '',
                  groupSize: 1,
                  travelStyle: '',
                  interests: []
                });
              }}
              className="bg-gray-600 hover:bg-gray-700 text-white px-6 py-3 rounded-lg transition-colors"
            >
              Plan Another Trip
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Creating Your Perfect Trip</h2>
          <p className="text-gray-600">Our AI is analyzing thousands of options to find the best recommendations for you...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 py-8 px-4">
      <div className="max-w-2xl mx-auto">
        {/* Progress Bar */}
        <div className="mb-8">
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm font-medium text-gray-700">Step {step} of 4</span>
            <span className="text-sm text-gray-500">{Math.round((step / 4) * 100)}% Complete</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${(step / 4) * 100}%` }}
            ></div>
          </div>
        </div>

        <div className="bg-white rounded-2xl shadow-xl p-8">
          {/* Step 1: Destination */}
          {step === 1 && (
            <div className="text-center">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                Where would you like to go?
              </h2>
              <p className="text-gray-600 mb-8">
                Tell us your dream destination and we'll create the perfect itinerary
              </p>
              <div className="relative">
                <i className="ri-map-pin-line absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 text-xl"></i>
                <input
                  type="text"
                  placeholder="Enter destination (e.g., Paris, Tokyo, New York)"
                  value={formData.destination}
                  onChange={(e) => handleInputChange('destination', e.target.value)}
                  className="w-full pl-12 pr-4 py-4 border-2 border-gray-200 rounded-xl text-lg focus:border-blue-500 focus:outline-none"
                />
              </div>
            </div>
          )}

          {/* Step 2: Duration & Budget */}
          {step === 2 && (
            <div className="text-center">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                Trip Details
              </h2>
              <p className="text-gray-600 mb-8">
                How long will you be traveling and what's your budget?
              </p>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-left text-gray-700 font-medium mb-2">
                    Duration (days)
                  </label>
                  <input
                    type="number"
                    placeholder="7"
                    value={formData.duration}
                    onChange={(e) => handleInputChange('duration', e.target.value)}
                    className="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:border-blue-500 focus:outline-none"
                    min="1"
                    max="30"
                  />
                </div>
                <div>
                  <label className="block text-left text-gray-700 font-medium mb-2">
                    Budget (USD)
                  </label>
                  <input
                    type="number"
                    placeholder="2000"
                    value={formData.budget}
                    onChange={(e) => handleInputChange('budget', e.target.value)}
                    className="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:border-blue-500 focus:outline-none"
                    min="100"
                  />
                </div>
              </div>
              <div className="mt-6">
                <label className="block text-left text-gray-700 font-medium mb-2">
                  Group Size
                </label>
                <select
                  value={formData.groupSize}
                  onChange={(e) => handleInputChange('groupSize', parseInt(e.target.value))}
                  className="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:border-blue-500 focus:outline-none"
                >
                  {[1,2,3,4,5,6,7,8].map(num => (
                    <option key={num} value={num}>
                      {num} {num === 1 ? 'person' : 'people'}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          )}

          {/* Step 3: Travel Style */}
          {step === 3 && (
            <div className="text-center">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                What's your travel style?
              </h2>
              <p className="text-gray-600 mb-8">
                Choose the style that best matches your preferences
              </p>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {travelStyles.map((style) => (
                  <button
                    key={style.id}
                    onClick={() => handleInputChange('travelStyle', style.id)}
                    className={`p-4 rounded-xl border-2 transition-all text-left ${
                      formData.travelStyle === style.id
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <div className="flex items-center mb-2">
                      <span className="text-2xl mr-3">{style.icon}</span>
                      <span className="font-bold text-gray-900">{style.name}</span>
                    </div>
                    <p className="text-gray-600 text-sm">{style.description}</p>
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* Step 4: Interests */}
          {step === 4 && (
            <div className="text-center">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                What interests you?
              </h2>
              <p className="text-gray-600 mb-8">
                Select all that apply to get personalized recommendations
              </p>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                {interests.map((interest) => (
                  <button
                    key={interest.id}
                    onClick={() => handleInterestToggle(interest.id)}
                    className={`p-3 rounded-xl border-2 transition-all ${
                      formData.interests.includes(interest.id)
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <div className="text-2xl mb-1">{interest.icon}</div>
                    <div className="text-sm font-medium text-gray-900">{interest.name}</div>
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* Navigation Buttons */}
          <div className="flex justify-between mt-8">
            <button
              onClick={prevStep}
              disabled={step === 1}
              className={`px-6 py-3 rounded-lg font-medium transition-colors ${
                step === 1 
                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed' 
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
            >
              Previous
            </button>
            <button
              onClick={nextStep}
              disabled={!canProceed()}
              className={`px-8 py-3 rounded-lg font-medium transition-colors ${
                canProceed()
                  ? 'bg-blue-600 text-white hover:bg-blue-700'
                  : 'bg-gray-200 text-gray-400 cursor-not-allowed'
              }`}
            >
              {step === 4 ? 'Generate Plan' : 'Next'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WonderPlanStyleAI;
