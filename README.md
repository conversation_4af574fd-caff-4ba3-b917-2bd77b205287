# TravelTrek :
## Description :
- Created a web application to assist users in discovering dream destinations and booking travel tickets.
- Features include view details and description of various destinations, ticket booking, and user reviews.
- Employed Tailwind CSS for designing the user interface of the application.
- Used Context API in ReactJS for user state management within the application.

## Technologies Used :
- ReactJs
- NodeJs
- ExpressJs
- MongoDB
- Tailwind CSS
- Cloudinary

## Installation :
1. Clone the repository
2. Install dependencies
```
npm install
```
3. Run the application
```
npm run dev
```
4. Open http://localhost:5173 to view it in the browser.

## Screenshots
**Home Page**

![Home](/screenshots/homepage.jpeg)

**Login Page**

![Login](/screenshots/loginpage.jpeg)

**Register page**

![Register](/screenshots/registerpage.jpeg)

**Tour details page**

![TourDetails](/screenshots/tourdetails.jpeg)

**Thank you page**

![Thankyou](/screenshots/thankyou.jpeg)
