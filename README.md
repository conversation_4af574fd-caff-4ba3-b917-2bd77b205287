# TravelTrek - AI Trip Planner :
## Description :
- Created an AI-powered web application to assist users in discovering dream destinations and creating personalized travel itineraries.
- Features include AI-generated travel recommendations, personalized trip planning, and intelligent destination suggestions.
- Inspired by WonderPlan.ai with a focus on AI-driven travel planning and recommendation system.
- Employed Tailwind CSS for designing a modern, clean user interface.
- Used Context API in ReactJS for user state management within the application.

## Key Features :
- **AI Trip Planner**: Intelligent travel planning with personalized recommendations
- **Smart Recommendations**: AI-powered suggestions for accommodations, activities, and dining
- **Interactive Planning**: Step-by-step trip creation with modern UI/UX
- **Personalized Itineraries**: Custom travel plans based on preferences and budget
- **No Booking System**: Focus purely on recommendations and planning (no booking functionality)

## Technologies Used :
- ReactJs
- NodeJs
- ExpressJs
- MongoDB
- Tailwind CSS
- AI Recommendation Engine

## Installation :
1. Clone the repository
2. Install dependencies for both client and server
```bash
# Install server dependencies
cd server
npm install

# Install client dependencies
cd ../client
npm install
```
3. Run the application
```bash
# Start the server (from server directory)
npm start

# Start the client (from client directory)
npm run dev
```
4. Open http://localhost:5173 to view the AI Trip Planner in the browser.

## Screenshots
**Home Page**

![Home](/screenshots/homepage.jpeg)

**Login Page**

![Login](/screenshots/loginpage.jpeg)

**Register page**

![Register](/screenshots/registerpage.jpeg)

**Tour details page**

![TourDetails](/screenshots/tourdetails.jpeg)

**Thank you page**

![Thankyou](/screenshots/thankyou.jpeg)
