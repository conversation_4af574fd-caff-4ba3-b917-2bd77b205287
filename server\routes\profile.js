const express = require('express');
const router = express.Router();
const {
  getUserProfile,
  updateUserProfile,
  updateUsername,
  updateEmail,
  changePassword,
  deleteUserAccount
} = require('../controllers/profileController');

// Get user profile
router.get('/', getUserProfile);

// Update user profile
router.put('/', updateUserProfile);

// Update username
router.put('/username', updateUsername);

// Update email
router.put('/email', updateEmail);

// Change password
router.put('/password', changePassword);

// Delete user account
router.delete('/', deleteUserAccount);

module.exports = router;
