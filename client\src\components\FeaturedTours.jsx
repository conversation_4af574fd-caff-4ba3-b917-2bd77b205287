
import TourCard from './TourCard';
import useFetch from '../hooks/useFetch';

const FeaturedTours = () => {
  
   const {data:TourData}=useFetch("http://localhost:9000/tours/getAllTours");
   //console.log(TourData);
      
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 sm:gap-8">
      {TourData?.map(tour => (
        <div key={tour._id} className="flex justify-center">
          <TourCard tour={tour}/>
        </div>
      ))}
    </div>
  )
}

export default FeaturedTours