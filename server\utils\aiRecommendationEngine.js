// AI Recommendation Engine for Travel Planner
// This simulates AI recommendations based on user preferences and travel data

const generatePersonalizedRecommendations = (preferences, destinations, budget, travelStyle) => {
  const recommendations = [];
  
  // Activity-based recommendations
  const activityRecommendations = generateActivityRecommendations(preferences.activities, destinations);
  recommendations.push(...activityRecommendations);
  
  // Budget-based recommendations
  const budgetRecommendations = generateBudgetRecommendations(budget, travelStyle, destinations);
  recommendations.push(...budgetRecommendations);
  
  // Style-based recommendations
  const styleRecommendations = generateStyleRecommendations(travelStyle, destinations);
  recommendations.push(...styleRecommendations);
  
  // Weather-based recommendations
  const weatherRecommendations = generateWeatherRecommendations(destinations);
  recommendations.push(...weatherRecommendations);
  
  // Local experience recommendations
  const localRecommendations = generateLocalExperienceRecommendations(destinations);
  recommendations.push(...localRecommendations);
  
  return recommendations.slice(0, 12); // Return top 12 recommendations
};

const generateActivityRecommendations = (activities, destinations) => {
  const recommendations = [];
  
  destinations.forEach(destination => {
    activities.forEach(activity => {
      switch (activity.toLowerCase()) {
        case 'adventure':
          recommendations.push({
            type: 'activity',
            title: `${destination.city} Adventure Sports`,
            description: 'Thrilling adventure activities including hiking, rock climbing, and water sports',
            location: destination.city,
            estimatedCost: 75,
            rating: 4.6,
            reasonForRecommendation: `Perfect for adventure enthusiasts visiting ${destination.city}`
          });
          break;
          
        case 'cultural':
          recommendations.push({
            type: 'tour',
            title: `${destination.city} Cultural Heritage Tour`,
            description: 'Explore historical sites, museums, and local cultural landmarks',
            location: destination.city,
            estimatedCost: 45,
            rating: 4.8,
            reasonForRecommendation: `Immerse yourself in the rich culture of ${destination.city}`
          });
          break;
          
        case 'food & drink':
          recommendations.push({
            type: 'restaurant',
            title: `${destination.city} Food Walking Tour`,
            description: 'Taste authentic local cuisine and visit the best restaurants',
            location: destination.city,
            estimatedCost: 60,
            rating: 4.7,
            reasonForRecommendation: `Discover the culinary delights of ${destination.city}`
          });
          break;
          
        case 'photography':
          recommendations.push({
            type: 'tour',
            title: `${destination.city} Photography Tour`,
            description: 'Capture stunning photos at the most photogenic locations',
            location: destination.city,
            estimatedCost: 55,
            rating: 4.5,
            reasonForRecommendation: `Perfect spots for photography enthusiasts in ${destination.city}`
          });
          break;
          
        case 'nature':
          recommendations.push({
            type: 'activity',
            title: `${destination.city} Nature Exploration`,
            description: 'Visit national parks, gardens, and natural scenic spots',
            location: destination.city,
            estimatedCost: 40,
            rating: 4.6,
            reasonForRecommendation: `Connect with nature in beautiful ${destination.city}`
          });
          break;
      }
    });
  });
  
  return recommendations;
};

const generateBudgetRecommendations = (budget, travelStyle, destinations) => {
  const recommendations = [];
  const budgetPerDestination = budget / destinations.length;
  
  destinations.forEach(destination => {
    if (budgetPerDestination < 500) {
      // Budget recommendations
      recommendations.push({
        type: 'accommodation',
        title: `Budget-Friendly Stay in ${destination.city}`,
        description: 'Clean, comfortable, and affordable accommodation options',
        location: destination.city,
        estimatedCost: 35,
        rating: 4.2,
        reasonForRecommendation: 'Great value accommodation that fits your budget'
      });
      
      recommendations.push({
        type: 'transport',
        title: `Public Transport Pass - ${destination.city}`,
        description: 'Unlimited access to buses, trains, and metro systems',
        location: destination.city,
        estimatedCost: 25,
        rating: 4.4,
        reasonForRecommendation: 'Cost-effective way to explore the city'
      });
    } else if (budgetPerDestination > 1000) {
      // Luxury recommendations
      recommendations.push({
        type: 'accommodation',
        title: `Luxury Resort in ${destination.city}`,
        description: 'Premium accommodation with world-class amenities',
        location: destination.city,
        estimatedCost: 350,
        rating: 4.9,
        reasonForRecommendation: 'Indulge in luxury that matches your travel style'
      });
      
      recommendations.push({
        type: 'transport',
        title: `Private Transfer Service - ${destination.city}`,
        description: 'Comfortable private transportation with professional driver',
        location: destination.city,
        estimatedCost: 120,
        rating: 4.8,
        reasonForRecommendation: 'Convenient and comfortable premium transport'
      });
    }
  });
  
  return recommendations;
};

const generateStyleRecommendations = (travelStyle, destinations) => {
  const recommendations = [];
  
  destinations.forEach(destination => {
    switch (travelStyle.toLowerCase()) {
      case 'family':
        recommendations.push({
          type: 'activity',
          title: `Family Fun Center - ${destination.city}`,
          description: 'Kid-friendly activities, games, and entertainment',
          location: destination.city,
          estimatedCost: 45,
          rating: 4.6,
          reasonForRecommendation: 'Perfect for family bonding and children\'s entertainment'
        });
        break;
        
      case 'luxury':
        recommendations.push({
          type: 'activity',
          title: `VIP Experience - ${destination.city}`,
          description: 'Exclusive access to premium attractions and services',
          location: destination.city,
          estimatedCost: 200,
          rating: 4.9,
          reasonForRecommendation: 'Exclusive luxury experience tailored for discerning travelers'
        });
        break;
        
      case 'backpacker':
        recommendations.push({
          type: 'activity',
          title: `Free Walking Tour - ${destination.city}`,
          description: 'Explore the city with local guides, tips-based tour',
          location: destination.city,
          estimatedCost: 15,
          rating: 4.5,
          reasonForRecommendation: 'Budget-friendly way to explore and meet fellow travelers'
        });
        break;
        
      case 'business':
        recommendations.push({
          type: 'accommodation',
          title: `Business Hotel - ${destination.city}`,
          description: 'Professional amenities, meeting rooms, and business center',
          location: destination.city,
          estimatedCost: 150,
          rating: 4.7,
          reasonForRecommendation: 'Ideal for business travelers with work facilities'
        });
        break;
    }
  });
  
  return recommendations;
};

const generateWeatherRecommendations = (destinations) => {
  const recommendations = [];
  
  destinations.forEach(destination => {
    // Simulate weather-based recommendations
    const currentMonth = new Date().getMonth();
    
    if (currentMonth >= 5 && currentMonth <= 8) { // Summer months
      recommendations.push({
        type: 'activity',
        title: `Summer Activities in ${destination.city}`,
        description: 'Beach visits, outdoor festivals, and water activities',
        location: destination.city,
        estimatedCost: 50,
        rating: 4.5,
        reasonForRecommendation: 'Perfect summer activities for the current season'
      });
    } else if (currentMonth >= 11 || currentMonth <= 2) { // Winter months
      recommendations.push({
        type: 'activity',
        title: `Winter Experiences in ${destination.city}`,
        description: 'Indoor attractions, winter sports, and cozy experiences',
        location: destination.city,
        estimatedCost: 65,
        rating: 4.4,
        reasonForRecommendation: 'Great winter activities to enjoy during your visit'
      });
    }
  });
  
  return recommendations;
};

const generateLocalExperienceRecommendations = (destinations) => {
  const recommendations = [];
  
  destinations.forEach(destination => {
    recommendations.push({
      type: 'activity',
      title: `Local Market Tour - ${destination.city}`,
      description: 'Experience authentic local life and shop for unique souvenirs',
      location: destination.city,
      estimatedCost: 30,
      rating: 4.6,
      reasonForRecommendation: 'Authentic local experience away from tourist crowds'
    });
    
    recommendations.push({
      type: 'tour',
      title: `Hidden Gems of ${destination.city}`,
      description: 'Discover secret spots and lesser-known attractions',
      location: destination.city,
      estimatedCost: 40,
      rating: 4.7,
      reasonForRecommendation: 'Unique experiences that most tourists miss'
    });
  });
  
  return recommendations;
};

const generateOptimalRoute = (itinerary) => {
  // Simple route optimization based on location proximity
  // In a real implementation, this would use mapping APIs
  
  const optimizedItinerary = [...itinerary];
  
  // Group by day and sort by city/location
  const groupedByDay = {};
  optimizedItinerary.forEach(item => {
    if (!groupedByDay[item.day]) {
      groupedByDay[item.day] = [];
    }
    groupedByDay[item.day].push(item);
  });
  
  // Sort each day's activities by location
  Object.keys(groupedByDay).forEach(day => {
    groupedByDay[day].sort((a, b) => {
      // Simple alphabetical sort by city - in real app would use coordinates
      return (a.tourId?.city || '').localeCompare(b.tourId?.city || '');
    });
  });
  
  // Flatten back to array
  const result = [];
  Object.keys(groupedByDay).sort((a, b) => parseInt(a) - parseInt(b)).forEach(day => {
    result.push(...groupedByDay[day]);
  });
  
  return result;
};

const calculateTravelTime = (location1, location2) => {
  // Simulate travel time calculation
  // In real implementation, would use Google Maps API or similar
  const baseTime = 30; // 30 minutes base time
  const randomFactor = Math.random() * 60; // 0-60 minutes additional
  return Math.round(baseTime + randomFactor);
};

const generateDynamicUpdates = (travelPlan, currentConditions) => {
  const updates = [];
  
  // Weather-based updates
  if (currentConditions.weather === 'rain') {
    updates.push({
      type: 'weather_alert',
      message: 'Rain expected - consider indoor activities',
      severity: 'medium',
      suggestions: ['Visit museums', 'Indoor shopping', 'Covered markets']
    });
  }
  
  // Budget alerts
  const spentPercentage = (travelPlan.currentSpent / travelPlan.totalBudget) * 100;
  if (spentPercentage > 80) {
    updates.push({
      type: 'budget_alert',
      message: 'You\'ve spent 80% of your budget',
      severity: 'high',
      suggestions: ['Look for free activities', 'Consider budget dining options']
    });
  }
  
  return updates;
};

module.exports = {
  generatePersonalizedRecommendations,
  generateOptimalRoute,
  calculateTravelTime,
  generateDynamicUpdates
};
