import React, { useState, useContext } from 'react';
import { AuthContext } from '../context/AuthContext';

const AISearchPlanner = () => {
  const { currentUser } = useContext(AuthContext);
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(false);
  const [itinerary, setItinerary] = useState(null);
  const [error, setError] = useState('');

  // Quick trip examples like WonderPlan
  const quickExamples = [
    {
      id: 1,
      title: "Planning a trip to Portugal",
      query: "Plan a 7-day trip to Portugal with cultural activities and local cuisine",
      destination: "Portugal",
      image: "🇵🇹"
    },
    {
      id: 2,
      title: "Planning a trip to Japan",
      query: "Plan a 10-day trip to Japan with temples, food tours, and modern attractions",
      destination: "Japan",
      image: "🇯🇵"
    },
    {
      id: 3,
      title: "Planning a trip to Korea",
      query: "Plan a 6-day trip to South Korea with K-pop culture and traditional sites",
      destination: "Korea",
      image: "🇰🇷"
    },
    {
      id: 4,
      title: "Planning a trip to Maldives",
      query: "Plan a 5-day romantic trip to Maldives with water activities and relaxation",
      destination: "Maldives",
      image: "🏝️"
    }
  ];

  // Enhanced natural language processing for travel queries
  const parseQuery = (query) => {
    const lowerQuery = query.toLowerCase();

    // Extract destination with multiple patterns
    let destination = '';
    const destinationPatterns = [
      /(?:to|in|visit|explore)\s+([a-zA-Z\s,]+?)(?:\s+for|\s+with|\s+during|\s+in\s+\d|\s*$)/,
      /trip\s+to\s+([a-zA-Z\s,]+?)(?:\s+for|\s+with|\s+during|\s+in\s+\d|\s*$)/,
      /vacation\s+(?:to|in)\s+([a-zA-Z\s,]+?)(?:\s+for|\s+with|\s+during|\s+in\s+\d|\s*$)/,
      /([a-zA-Z\s,]+?)\s+(?:trip|vacation|holiday|tour)(?:\s+for|\s+with|\s+during|\s+in\s+\d|\s*$)/
    ];

    for (const pattern of destinationPatterns) {
      const match = lowerQuery.match(pattern);
      if (match) {
        destination = match[1].trim();
        // Clean up common words
        destination = destination.replace(/\b(a|an|the|trip|vacation|holiday|tour)\b/g, '').trim();
        break;
      }
    }

    // Extract duration with various formats
    let duration = 7; // default
    const durationPatterns = [
      /(\d+)[\s-]*(day|days)/,
      /(\d+)[\s-]*(week|weeks)/,
      /(\d+)[\s-]*(night|nights)/,
      /for\s+(\d+)\s+days?/,
      /(\d+)[\s-]*day/
    ];

    for (const pattern of durationPatterns) {
      const match = lowerQuery.match(pattern);
      if (match) {
        const num = parseInt(match[1]);
        const unit = match[2];
        if (unit.includes('week')) {
          duration = num * 7;
        } else if (unit.includes('night')) {
          duration = num + 1;
        } else {
          duration = num;
        }
        break;
      }
    }

    // Extract budget with various formats
    let budget = 2000; // default
    const budgetPatterns = [
      /\$(\d+(?:,\d{3})*)/,
      /budget\s+(?:of\s+)?\$?(\d+(?:,\d{3})*)/,
      /(\d+(?:,\d{3})*)\s*dollars?/,
      /under\s+\$?(\d+(?:,\d{3})*)/,
      /around\s+\$?(\d+(?:,\d{3})*)/
    ];

    for (const pattern of budgetPatterns) {
      const match = lowerQuery.match(pattern);
      if (match) {
        budget = parseInt(match[1].replace(/,/g, ''));
        break;
      }
    }

    // Extract travel style
    let travelStyle = 'mid-range';
    if (lowerQuery.includes('budget') || lowerQuery.includes('cheap') || lowerQuery.includes('affordable')) {
      travelStyle = 'budget';
    } else if (lowerQuery.includes('luxury') || lowerQuery.includes('premium') || lowerQuery.includes('high-end')) {
      travelStyle = 'luxury';
    } else if (lowerQuery.includes('family') || lowerQuery.includes('kids') || lowerQuery.includes('children')) {
      travelStyle = 'family';
    } else if (lowerQuery.includes('romantic') || lowerQuery.includes('honeymoon') || lowerQuery.includes('couple')) {
      travelStyle = 'romantic';
    } else if (lowerQuery.includes('adventure') || lowerQuery.includes('hiking') || lowerQuery.includes('outdoor')) {
      travelStyle = 'adventure';
    }

    // Extract group size
    let groupSize = 1;
    const groupPatterns = [
      /(\d+)\s+people/,
      /group\s+of\s+(\d+)/,
      /(\d+)\s+person/,
      /(\d+)\s+travelers?/,
      /(\d+)\s+adults?/
    ];

    for (const pattern of groupPatterns) {
      const match = lowerQuery.match(pattern);
      if (match) {
        groupSize = parseInt(match[1]);
        break;
      }
    }

    // Extract activities/interests with enhanced detection
    const interests = [];
    const interestKeywords = {
      'cultural': ['cultural', 'culture', 'museum', 'history', 'historical', 'heritage', 'art', 'gallery', 'monument'],
      'food': ['food', 'cuisine', 'restaurant', 'dining', 'culinary', 'cooking', 'local dishes', 'street food'],
      'adventure': ['adventure', 'hiking', 'climbing', 'outdoor', 'extreme', 'sports', 'trekking', 'safari'],
      'romantic': ['romantic', 'romance', 'honeymoon', 'couple', 'intimate', 'sunset', 'wine'],
      'family': ['family', 'kids', 'children', 'family-friendly', 'playground', 'theme park'],
      'beach': ['beach', 'ocean', 'sea', 'swimming', 'snorkeling', 'diving', 'water sports', 'coastal'],
      'religious': ['temple', 'church', 'mosque', 'religious', 'spiritual', 'pilgrimage', 'sacred'],
      'shopping': ['shopping', 'market', 'mall', 'boutique', 'souvenir', 'local crafts'],
      'nightlife': ['nightlife', 'party', 'club', 'bar', 'entertainment', 'music', 'dancing'],
      'nature': ['nature', 'park', 'wildlife', 'garden', 'forest', 'mountain', 'scenic'],
      'photography': ['photography', 'photo', 'instagram', 'scenic views', 'landmarks']
    };

    for (const [interest, keywords] of Object.entries(interestKeywords)) {
      if (keywords.some(keyword => lowerQuery.includes(keyword))) {
        interests.push(interest);
      }
    }

    // If no specific interests found, add some defaults based on destination
    if (interests.length === 0) {
      interests.push('cultural', 'food');
    }

    return { destination, duration, budget, interests, travelStyle, groupSize };
  };

  // Enhanced AI itinerary generator with destination-specific content
  const generateItinerary = async (parsedQuery) => {
    const { destination, duration, budget, interests, travelStyle, groupSize } = parsedQuery;

    // Simulate AI processing with realistic timing
    await new Promise(resolve => setTimeout(resolve, 3000));

    const dailyBudget = Math.floor(budget / duration);

    // Get destination-specific information
    const destinationInfo = getDestinationInfo(destination);

    // Generate day-by-day itinerary with smart planning
    const days = [];
    for (let i = 1; i <= duration; i++) {
      const day = {
        day: i,
        title: generateDayTitle(destination, i, duration, destinationInfo),
        activities: generateDayActivities(destination, i, interests, dailyBudget, travelStyle, groupSize, destinationInfo, duration),
        totalCost: dailyBudget,
        weather: getWeatherTip(destination, i),
        tips: getDayTips(destination, i, duration, interests)
      };
      days.push(day);
    }

    return {
      destination,
      duration,
      totalBudget: budget,
      travelStyle,
      groupSize,
      days,
      summary: {
        accommodation: Math.floor(budget * 0.4),
        food: Math.floor(budget * 0.3),
        activities: Math.floor(budget * 0.2),
        transport: Math.floor(budget * 0.1)
      },
      recommendations: generateGeneralRecommendations(destination, interests, travelStyle)
    };
  };

  // Get destination-specific information
  const getDestinationInfo = (destination) => {
    const destinations = {
      'portugal': {
        highlights: ['Lisbon', 'Porto', 'Sintra', 'Óbidos', 'Coimbra'],
        cuisine: ['Pastéis de nata', 'Francesinha', 'Bacalhau', 'Port wine'],
        culture: ['Fado music', 'Azulejo tiles', 'Maritime history'],
        activities: ['Tram 28', 'Douro Valley', 'Pena Palace', 'Jerónimos Monastery']
      },
      'japan': {
        highlights: ['Tokyo', 'Kyoto', 'Osaka', 'Hiroshima', 'Mount Fuji'],
        cuisine: ['Sushi', 'Ramen', 'Tempura', 'Sake', 'Wagyu beef'],
        culture: ['Temples', 'Tea ceremony', 'Cherry blossoms', 'Anime culture'],
        activities: ['Shibuya Crossing', 'Fushimi Inari', 'Tsukiji Market', 'Bullet train']
      },
      'korea': {
        highlights: ['Seoul', 'Busan', 'Jeju Island', 'Gyeongju', 'DMZ'],
        cuisine: ['Kimchi', 'Bulgogi', 'Bibimbap', 'Korean BBQ', 'Soju'],
        culture: ['K-pop', 'Hanbok', 'Buddhist temples', 'Traditional markets'],
        activities: ['Gangnam District', 'Bukchon Hanok Village', 'Haeundae Beach', 'Lotte Tower']
      },
      'maldives': {
        highlights: ['Malé', 'Ari Atoll', 'Baa Atoll', 'Vaavu Atoll'],
        cuisine: ['Fresh seafood', 'Coconut curry', 'Tropical fruits', 'Maldivian fish curry'],
        culture: ['Island life', 'Traditional dhoni boats', 'Local fishing'],
        activities: ['Snorkeling', 'Diving', 'Water sports', 'Sunset cruises', 'Spa treatments']
      }
    };

    const key = destination.toLowerCase();
    return destinations[key] || {
      highlights: [`${destination} city center`, `${destination} old town`, `${destination} landmarks`],
      cuisine: ['Local specialties', 'Traditional dishes', 'Street food'],
      culture: ['Local traditions', 'Historical sites', 'Art and music'],
      activities: ['City tour', 'Local experiences', 'Cultural sites']
    };
  };

  // Generate smart day titles
  const generateDayTitle = (destination, day, totalDays, destinationInfo) => {
    if (day === 1) return `Welcome to ${destination}`;
    if (day === totalDays) return `Farewell ${destination}`;

    const midPoint = Math.ceil(totalDays / 2);
    if (day === midPoint && destinationInfo.highlights.length > 1) {
      return `Discover ${destinationInfo.highlights[1]}`;
    }

    const titles = [
      `Exploring ${destination}`,
      `Cultural ${destination}`,
      `Hidden Gems of ${destination}`,
      `Local Life in ${destination}`,
      `Adventures in ${destination}`
    ];

    return titles[(day - 2) % titles.length];
  };

  // Enhanced day activities generation with destination-specific content
  const generateDayActivities = (destination, dayNumber, interests, dailyBudget, travelStyle, groupSize, destinationInfo, totalDays) => {
    const activities = [];
    const budgetMultiplier = travelStyle === 'luxury' ? 1.5 : travelStyle === 'budget' ? 0.7 : 1;

    // Morning activity
    if (dayNumber === 1) {
      activities.push({
        time: "9:00 AM",
        title: `Welcome to ${destination}`,
        description: `Airport transfer and check-in to your ${travelStyle} accommodation. Brief orientation and local SIM card setup.`,
        type: "logistics",
        cost: Math.floor(50 * budgetMultiplier),
        duration: "3 hours",
        tips: "Keep your passport and important documents handy"
      });

      activities.push({
        time: "2:00 PM",
        title: `First Taste of ${destination}`,
        description: `Welcome lunch featuring ${destinationInfo.cuisine[0]} and other local specialties`,
        type: "food",
        cost: Math.floor(dailyBudget * 0.2 * budgetMultiplier),
        duration: "1.5 hours",
        tips: "Perfect introduction to local flavors"
      });

      activities.push({
        time: "4:00 PM",
        title: `${destination} Orientation Walk`,
        description: `Gentle walking tour around your accommodation area to get familiar with the neighborhood`,
        type: "sightseeing",
        cost: 0,
        duration: "2 hours",
        tips: "Great for beating jet lag and getting oriented"
      });
    } else if (dayNumber === totalDays) {
      // Last day activities
      activities.push({
        time: "9:00 AM",
        title: "Last-Minute Shopping",
        description: `Visit ${destinationInfo.highlights[0]} for souvenirs and last-minute purchases`,
        type: "shopping",
        cost: Math.floor(dailyBudget * 0.3),
        duration: "2 hours",
        tips: "Don't forget to check customs regulations"
      });

      activities.push({
        time: "12:00 PM",
        title: "Farewell Lunch",
        description: `Final meal featuring your favorite ${destination} dishes discovered during your trip`,
        type: "food",
        cost: Math.floor(dailyBudget * 0.25 * budgetMultiplier),
        duration: "1.5 hours",
        tips: "Savor the last moments of your journey"
      });

      activities.push({
        time: "3:00 PM",
        title: "Departure",
        description: `Check-out and transfer to airport. Safe travels!`,
        type: "logistics",
        cost: Math.floor(40 * budgetMultiplier),
        duration: "2 hours",
        tips: "Arrive at airport 2-3 hours before international flights"
      });
    } else {
      // Regular day activities
      const morningActivity = generateMorningActivity(destination, dayNumber, interests, dailyBudget, destinationInfo, budgetMultiplier);
      activities.push(morningActivity);

      // Lunch
      const lunchActivity = generateLunchActivity(destination, dayNumber, interests, dailyBudget, destinationInfo, budgetMultiplier);
      activities.push(lunchActivity);

      // Afternoon activity
      const afternoonActivity = generateAfternoonActivity(destination, dayNumber, interests, dailyBudget, destinationInfo, budgetMultiplier, travelStyle);
      activities.push(afternoonActivity);

      // Evening activity
      const eveningActivity = generateEveningActivity(destination, dayNumber, interests, dailyBudget, destinationInfo, budgetMultiplier, travelStyle);
      activities.push(eveningActivity);
    }

    return activities;
  };

  // Generate morning activities
  const generateMorningActivity = (destination, day, interests, dailyBudget, destinationInfo, budgetMultiplier) => {
    const culturalActivities = [
      {
        title: `${destinationInfo.highlights[0]} Cultural Tour`,
        description: `Guided tour of ${destinationInfo.highlights[0]} including ${destinationInfo.culture[0]} and historical landmarks`,
        cost: Math.floor(dailyBudget * 0.25 * budgetMultiplier),
        tips: "Book skip-the-line tickets in advance"
      },
      {
        title: `${destination} Museum District`,
        description: `Explore world-class museums and art galleries showcasing ${destinationInfo.culture[1]}`,
        cost: Math.floor(dailyBudget * 0.2 * budgetMultiplier),
        tips: "Many museums offer free hours or discounts"
      }
    ];

    const adventureActivities = [
      {
        title: `${destination} Adventure Experience`,
        description: `Outdoor adventure activity featuring the natural beauty around ${destination}`,
        cost: Math.floor(dailyBudget * 0.35 * budgetMultiplier),
        tips: "Wear comfortable shoes and bring water"
      },
      {
        title: `Active ${destination} Tour`,
        description: `Bike or hiking tour exploring ${destination}'s scenic routes and hidden gems`,
        cost: Math.floor(dailyBudget * 0.3 * budgetMultiplier),
        tips: "Check weather conditions before departure"
      }
    ];

    let selectedActivity;
    if (interests.includes('cultural')) {
      selectedActivity = culturalActivities[day % culturalActivities.length];
    } else if (interests.includes('adventure')) {
      selectedActivity = adventureActivities[day % adventureActivities.length];
    } else {
      selectedActivity = {
        title: `Discover ${destinationInfo.highlights[day % destinationInfo.highlights.length]}`,
        description: `Explore the iconic ${destinationInfo.highlights[day % destinationInfo.highlights.length]} and surrounding attractions`,
        cost: Math.floor(dailyBudget * 0.25 * budgetMultiplier),
        tips: "Perfect for photos and learning local history"
      };
    }

    return {
      time: "9:00 AM",
      ...selectedActivity,
      type: interests.includes('cultural') ? 'cultural' : interests.includes('adventure') ? 'adventure' : 'sightseeing',
      duration: "3 hours"
    };
  };

  // Generate lunch activities
  const generateLunchActivity = (destination, day, interests, dailyBudget, destinationInfo, budgetMultiplier) => {
    const cuisineOptions = [
      {
        title: `Authentic ${destinationInfo.cuisine[0]} Experience`,
        description: `Traditional restaurant serving the best ${destinationInfo.cuisine[0]} in ${destination}`,
        tips: "Try the chef's special recommendation"
      },
      {
        title: `Local Food Market Tour`,
        description: `Explore local food markets and try various ${destination} street foods and specialties`,
        tips: "Great way to interact with locals"
      },
      {
        title: `${destinationInfo.cuisine[1]} Cooking Class`,
        description: `Learn to cook traditional ${destinationInfo.cuisine[1]} with a local chef`,
        tips: "You'll get recipes to take home"
      }
    ];

    const selectedLunch = cuisineOptions[day % cuisineOptions.length];

    return {
      time: "12:30 PM",
      ...selectedLunch,
      type: "food",
      cost: Math.floor(dailyBudget * 0.2 * budgetMultiplier),
      duration: "1.5 hours"
    };
  };

  // Generate afternoon activities
  const generateAfternoonActivity = (destination, day, interests, dailyBudget, destinationInfo, budgetMultiplier, travelStyle) => {
    if (interests.includes('shopping')) {
      return {
        time: "3:00 PM",
        title: `${destination} Shopping Experience`,
        description: `Explore local markets, boutiques, and shopping districts for unique souvenirs and local crafts`,
        type: "shopping",
        cost: Math.floor(dailyBudget * 0.3 * budgetMultiplier),
        duration: "3 hours",
        tips: "Don't forget to bargain at local markets"
      };
    } else if (interests.includes('nature')) {
      return {
        time: "3:00 PM",
        title: `${destination} Natural Wonders`,
        description: `Visit parks, gardens, or natural attractions around ${destination}`,
        type: "nature",
        cost: Math.floor(dailyBudget * 0.25 * budgetMultiplier),
        duration: "3 hours",
        tips: "Perfect time for photography with good lighting"
      };
    } else if (interests.includes('beach') && destination.toLowerCase().includes('maldives')) {
      return {
        time: "3:00 PM",
        title: "Water Sports & Beach Time",
        description: "Snorkeling, diving, or relaxing on pristine beaches with crystal clear waters",
        type: "beach",
        cost: Math.floor(dailyBudget * 0.4 * budgetMultiplier),
        duration: "4 hours",
        tips: "Don't forget sunscreen and underwater camera"
      };
    } else {
      return {
        time: "3:00 PM",
        title: `${destinationInfo.activities[day % destinationInfo.activities.length]}`,
        description: `Experience one of ${destination}'s most popular attractions and activities`,
        type: "sightseeing",
        cost: Math.floor(dailyBudget * 0.25 * budgetMultiplier),
        duration: "3 hours",
        tips: "Check opening hours and book tickets in advance"
      };
    }
  };

  // Generate evening activities
  const generateEveningActivity = (destination, day, interests, dailyBudget, destinationInfo, budgetMultiplier, travelStyle) => {
    if (interests.includes('nightlife')) {
      return {
        time: "7:00 PM",
        title: `${destination} Nightlife Experience`,
        description: `Dinner followed by exploring ${destination}'s vibrant nightlife scene`,
        type: "nightlife",
        cost: Math.floor(dailyBudget * 0.3 * budgetMultiplier),
        duration: "4 hours",
        tips: "Check local customs and dress codes"
      };
    } else if (interests.includes('romantic')) {
      return {
        time: "7:00 PM",
        title: "Romantic Evening",
        description: `Intimate dinner at a highly-rated restaurant with beautiful views of ${destination}`,
        type: "romantic",
        cost: Math.floor(dailyBudget * 0.35 * budgetMultiplier),
        duration: "3 hours",
        tips: "Make reservations in advance for best tables"
      };
    } else {
      return {
        time: "7:00 PM",
        title: "Evening Dining & Relaxation",
        description: `Dinner at a recommended local restaurant followed by a leisurely evening stroll`,
        type: "food",
        cost: Math.floor(dailyBudget * 0.25 * budgetMultiplier),
        duration: "3 hours",
        tips: "Try local specialties and enjoy the evening atmosphere"
      };
    }
  };

  // Generate weather tips
  const getWeatherTip = (destination, day) => {
    const tips = [
      "Check weather forecast and dress accordingly",
      "Perfect day for outdoor activities",
      "Great weather for sightseeing",
      "Ideal conditions for photography",
      "Good day to explore on foot"
    ];
    return tips[day % tips.length];
  };

  // Generate day-specific tips
  const getDayTips = (destination, day, totalDays, interests) => {
    if (day === 1) return "Take it easy on your first day to adjust to the new environment";
    if (day === totalDays) return "Pack early and double-check your departure details";
    if (day === Math.ceil(totalDays / 2)) return "Perfect time to try something adventurous!";
    return "Stay hydrated and take breaks when needed";
  };

  // Generate general recommendations
  const generateGeneralRecommendations = (destination, interests, travelStyle) => {
    return [
      {
        category: "Transportation",
        tip: `Use local public transport or ride-sharing apps for authentic ${destination} experience`,
        icon: "ri-bus-line"
      },
      {
        category: "Money",
        tip: "Notify your bank about travel plans and keep some local currency handy",
        icon: "ri-money-dollar-circle-line"
      },
      {
        category: "Safety",
        tip: "Keep copies of important documents and stay aware of your surroundings",
        icon: "ri-shield-check-line"
      },
      {
        category: "Culture",
        tip: `Learn basic local phrases and respect ${destination}'s customs and traditions`,
        icon: "ri-global-line"
      }
    ];
  };

  const handleSearch = async () => {
    if (!searchQuery.trim()) {
      setError('Please enter a travel query');
      return;
    }
    
    setLoading(true);
    setError('');
    
    try {
      const parsedQuery = parseQuery(searchQuery);
      
      if (!parsedQuery.destination) {
        setError('Please specify a destination in your query');
        setLoading(false);
        return;
      }
      
      const generatedItinerary = await generateItinerary(parsedQuery);
      setItinerary(generatedItinerary);
    } catch (err) {
      setError('Failed to generate itinerary. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleExampleClick = async (example) => {
    setSearchQuery(example.query);
    setLoading(true);
    setError('');
    
    try {
      const parsedQuery = parseQuery(example.query);
      const generatedItinerary = await generateItinerary(parsedQuery);
      setItinerary(generatedItinerary);
    } catch (err) {
      setError('Failed to generate itinerary. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  const getActivityIcon = (type) => {
    switch (type) {
      case 'cultural': return 'ri-building-line';
      case 'food': return 'ri-restaurant-line';
      case 'adventure': return 'ri-mountain-line';
      case 'shopping': return 'ri-shopping-bag-line';
      case 'sightseeing': return 'ri-camera-line';
      case 'nightlife': return 'ri-music-line';
      case 'logistics': return 'ri-plane-line';
      case 'romantic': return 'ri-heart-line';
      case 'beach': return 'ri-sun-line';
      case 'religious': return 'ri-ancient-gate-line';
      default: return 'ri-map-pin-line';
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto px-4">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-6"></div>
          <h2 className="text-2xl font-bold text-gray-900 mb-3">Creating Your Perfect Itinerary</h2>
          <p className="text-gray-600 mb-4">Our AI is analyzing your preferences and crafting a personalized travel plan...</p>
          <div className="bg-white rounded-lg p-4 shadow-lg">
            <div className="flex items-center justify-center space-x-2 text-sm text-gray-500">
              <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
              <span>Finding best attractions</span>
            </div>
            <div className="flex items-center justify-center space-x-2 text-sm text-gray-500 mt-2">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse animation-delay-200"></div>
              <span>Selecting restaurants</span>
            </div>
            <div className="flex items-center justify-center space-x-2 text-sm text-gray-500 mt-2">
              <div className="w-2 h-2 bg-purple-500 rounded-full animate-pulse animation-delay-400"></div>
              <span>Optimizing schedule</span>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50">
      {!itinerary ? (
        // Search Interface
        <div className="flex flex-col justify-center items-center min-h-screen px-4">
          <div className="text-center max-w-4xl mx-auto mb-12">
            <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-gray-900 mb-6 leading-tight">
              <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                Craft Unforgettable
              </span>
              <br />
              <span className="text-gray-800">Itineraries with AI</span>
            </h1>
            <p className="text-lg sm:text-xl text-gray-600 mb-8">
              Your personal trip planner and travel curator, creating custom itineraries 
              tailored to your interests and budget.
            </p>
            <p className="text-base text-gray-500 mb-12">
              Get started—it's free
            </p>
          </div>

          {/* AI Search Bar */}
          <div className="w-full max-w-3xl mx-auto mb-12">
            <div className="bg-white rounded-2xl shadow-xl p-6">
              <div className="flex items-center space-x-4">
                <div className="flex-1">
                  <input
                    type="text"
                    placeholder="Try: 'Plan a 7-day trip to Paris with cultural activities and local cuisine'"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    onKeyPress={handleKeyPress}
                    className="w-full px-4 py-4 text-lg border-2 border-gray-200 rounded-xl focus:border-blue-500 focus:outline-none"
                  />
                </div>
                <button
                  onClick={handleSearch}
                  disabled={!searchQuery.trim()}
                  className={`px-8 py-4 rounded-xl font-semibold transition-all ${
                    searchQuery.trim()
                      ? 'bg-blue-600 hover:bg-blue-700 text-white shadow-lg hover:shadow-xl'
                      : 'bg-gray-200 text-gray-400 cursor-not-allowed'
                  }`}
                >
                  <i className="ri-magic-line mr-2"></i>
                  Plan Trip
                </button>
              </div>
              {error && (
                <p className="text-red-500 text-sm mt-3">{error}</p>
              )}
            </div>
          </div>

          {/* Quick Examples */}
          <div className="w-full max-w-5xl mx-auto">
            <h3 className="text-xl font-semibold text-gray-800 text-center mb-6">
              Get started with these popular destinations
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {quickExamples.map((example) => (
                <button
                  key={example.id}
                  onClick={() => handleExampleClick(example)}
                  className="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 text-left"
                >
                  <div className="text-3xl mb-3">{example.image}</div>
                  <h4 className="font-semibold text-gray-900 mb-2">{example.title}</h4>
                  <p className="text-sm text-gray-600">{example.destination}</p>
                </button>
              ))}
            </div>
          </div>
        </div>
      ) : (
        // Itinerary Results - This will be added in the next part
        <div className="p-8">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-8">
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                Your {itinerary.duration}-Day {itinerary.destination} Itinerary
              </h1>
              <p className="text-gray-600">
                AI-crafted travel plan • Total Budget: ${itinerary.totalBudget}
              </p>
            </div>
            
            {/* Budget Summary */}
            <div className="bg-white rounded-xl shadow-lg p-6 mb-8">
              <h3 className="text-lg font-semibold mb-4">Budget Breakdown</h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">${itinerary.summary.accommodation}</div>
                  <div className="text-sm text-gray-600">Accommodation</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">${itinerary.summary.food}</div>
                  <div className="text-sm text-gray-600">Food & Dining</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-600">${itinerary.summary.activities}</div>
                  <div className="text-sm text-gray-600">Activities</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-orange-600">${itinerary.summary.transport}</div>
                  <div className="text-sm text-gray-600">Transport</div>
                </div>
              </div>
            </div>

            {/* Day-by-day Itinerary */}
            <div className="space-y-6 mb-8">
              {itinerary.days.map((day) => (
                <div key={day.day} className="bg-white rounded-xl shadow-lg overflow-hidden">
                  <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-4">
                    <h3 className="text-xl font-bold">Day {day.day}: {day.title}</h3>
                    <p className="text-blue-100">Daily Budget: ${day.totalCost}</p>
                  </div>
                  <div className="p-6">
                    <div className="space-y-4">
                      {day.activities.map((activity, index) => (
                        <div key={index} className="flex items-start space-x-4 p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                          <div className="flex-shrink-0">
                            <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                              <i className={`${getActivityIcon(activity.type)} text-blue-600`}></i>
                            </div>
                          </div>
                          <div className="flex-1">
                            <div className="flex justify-between items-start mb-2">
                              <div>
                                <h4 className="font-semibold text-gray-900">{activity.title}</h4>
                                <p className="text-sm text-gray-600">{activity.time} • {activity.duration}</p>
                              </div>
                              <div className="text-right">
                                <div className="font-semibold text-gray-900">${activity.cost}</div>
                                <div className="text-xs text-gray-500 capitalize">{activity.type}</div>
                              </div>
                            </div>
                            <p className="text-gray-700 mb-2">{activity.description}</p>
                            {activity.tips && (
                              <div className="flex items-center text-sm text-blue-600 bg-blue-50 px-3 py-1 rounded-full">
                                <i className="ri-lightbulb-line mr-1"></i>
                                <span>{activity.tips}</span>
                              </div>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Travel Recommendations */}
            {itinerary.recommendations && (
              <div className="bg-white rounded-xl shadow-lg p-6 mb-8">
                <h3 className="text-xl font-bold text-gray-900 mb-4">
                  <i className="ri-lightbulb-line mr-2 text-yellow-500"></i>
                  Travel Tips & Recommendations
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {itinerary.recommendations.map((rec, index) => (
                    <div key={index} className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
                      <i className={`${rec.icon} text-blue-600 text-lg mt-1`}></i>
                      <div>
                        <h4 className="font-semibold text-gray-900 mb-1">{rec.category}</h4>
                        <p className="text-sm text-gray-600">{rec.tip}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Action Buttons */}
            <div className="text-center space-x-4">
              <button
                onClick={() => {
                  setItinerary(null);
                  setSearchQuery('');
                }}
                className="bg-gray-600 hover:bg-gray-700 text-white px-6 py-3 rounded-lg transition-colors"
              >
                Plan Another Trip
              </button>
              <button
                onClick={() => {
                  // Save itinerary functionality can be added here
                  alert('Save functionality will be implemented');
                }}
                className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg transition-colors"
              >
                Save Itinerary
              </button>
              <button
                onClick={() => {
                  // Download PDF functionality can be added here
                  alert('PDF download functionality will be implemented');
                }}
                className="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg transition-colors"
              >
                Download PDF
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AISearchPlanner;
