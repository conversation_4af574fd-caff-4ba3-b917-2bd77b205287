{"name": "client", "private": true, "version": "0.0.0", "type": "module", "scripts": {"predeploy": "npm run build", "deploy": "gh-pages -d dist", "dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@google-pay/button-react": "^3.0.9", "axios": "^1.3.4", "flowbite": "^1.6.3", "flowbite-react": "^0.3.8", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.1", "react-stripe-checkout": "^2.6.3", "react-toastify": "^9.1.3", "remixicon": "^2.5.0"}, "devDependencies": {"@types/react": "^18.0.27", "@types/react-dom": "^18.0.10", "@vitejs/plugin-react": "^3.1.0", "autoprefixer": "^10.4.13", "gh-pages": "^5.0.0", "postcss": "^8.4.21", "tailwind-scrollbar": "^2.1.0", "tailwindcss": "^3.2.5", "vite": "^4.1.0"}, "proxy": "https://traveltrek.onrender.com/"}