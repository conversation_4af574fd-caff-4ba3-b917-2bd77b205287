import { useRef,useState } from "react";
import { useNavigate } from "react-router-dom";
import axios from 'axios';

const SearchBar = () => {
  const navigate = useNavigate();
  const locationRef = useRef("");
  const distanceRef = useRef(0);
  const maxGroupSizeRef = useRef(0);
  const [error,setError]=useState(false);
  
  const searchHandler = async (e) => {
  e.preventDefault();
  const location = locationRef.current.value;
  const distance = distanceRef.current.value;
  const maxGroupSize = maxGroupSizeRef.current.value;
    
  if (location === "" || distance === "" || maxGroupSize === 0) {
      alert("Please fill all the fields");
  }
    
  const res = await axios.get(
    `http://localhost:9000/tours/getTourBySearch?city=${location}&distance=${distance}&maxGroupSize=${maxGroupSize}`,
    {headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    }}
    );
  console.log(res);
    if (res.ok) {
      alert('Something went wrong');
    }else{
    const result = res.data;
    navigate(
      `/search?city=${location}&distance=${distance}&maxGroupSize=${maxGroupSize}`,
      { state: result.data });
    }
  };

  return (
    <div className="bg-white/90 backdrop-blur-sm rounded-2xl lg:rounded-full shadow-xl mx-auto max-w-6xl">
      <form onSubmit={searchHandler}>
        <div className="flex flex-col lg:flex-row items-center justify-center p-4 lg:p-6 gap-4 lg:gap-6">

          {/* Location Input */}
          <div className="flex items-center w-full lg:w-auto min-w-0 lg:flex-1">
            <div className="flex items-center bg-gray-50 rounded-full px-4 py-3 w-full">
              <i className="ri-map-pin-line text-pink-600 mr-2 text-lg"></i>
              <div className="flex flex-col flex-1 min-w-0">
                <label className="text-xs font-medium text-gray-600 mb-1">Location</label>
                <input
                  type="text"
                  name="location"
                  placeholder="Where are you going?"
                  className="bg-transparent border-none outline-none text-gray-800 placeholder-gray-400 text-sm w-full"
                  ref={locationRef}
                />
              </div>
            </div>
          </div>

          {/* Distance Input */}
          <div className="flex items-center w-full lg:w-auto min-w-0 lg:flex-1">
            <div className="flex items-center bg-gray-50 rounded-full px-4 py-3 w-full">
              <i className="ri-pin-distance-line text-pink-600 mr-2 text-lg"></i>
              <div className="flex flex-col flex-1 min-w-0">
                <label className="text-xs font-medium text-gray-600 mb-1">Distance</label>
                <input
                  type="number"
                  name="distance"
                  placeholder="Distance in km"
                  className="bg-transparent border-none outline-none text-gray-800 placeholder-gray-400 text-sm w-full"
                  ref={distanceRef}
                />
              </div>
            </div>
          </div>

          {/* Group Size Input */}
          <div className="flex items-center w-full lg:w-auto min-w-0 lg:flex-1">
            <div className="flex items-center bg-gray-50 rounded-full px-4 py-3 w-full">
              <i className="ri-group-line text-pink-600 mr-2 text-lg"></i>
              <div className="flex flex-col flex-1 min-w-0">
                <label className="text-xs font-medium text-gray-600 mb-1">Max People</label>
                <input
                  type="number"
                  name="numberofpeople"
                  placeholder="0"
                  className="bg-transparent border-none outline-none text-gray-800 placeholder-gray-400 text-sm w-full"
                  ref={maxGroupSizeRef}
                />
              </div>
            </div>
          </div>

          {/* Search Button */}
          <div className="w-full lg:w-auto">
            <button
              type="submit"
              className="w-full lg:w-auto bg-gradient-to-r from-pink-600 to-purple-600 hover:from-pink-700 hover:to-purple-700 text-white font-semibold py-3 px-8 rounded-full shadow-lg transform hover:scale-105 transition-all duration-200 flex items-center justify-center"
            >
              <i className="ri-search-line mr-2 text-lg"></i>
              Search Tours
            </button>
          </div>
        </div>
      </form>
      {error && (
        <div className="px-6 pb-4">
          <p className="text-red-500 text-center text-sm">No tours found</p>
        </div>
      )}
    </div>
  );
};

export default SearchBar;
