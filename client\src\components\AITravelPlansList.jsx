import React, { useContext } from 'react';
import { useNavigate } from 'react-router-dom';
import { AuthContext } from '../context/AuthContext';
import useFetch from '../hooks/useFetch';

const AITravelPlansList = () => {
  const { currentUser } = useContext(AuthContext);
  const navigate = useNavigate();
  
  const { data: travelPlans, loading } = useFetch(
    currentUser ? `http://localhost:9000/ai-travel-planner/user/${currentUser._id}` : null
  );

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const calculateDuration = (startDate, endDate) => {
    return Math.ceil((new Date(endDate) - new Date(startDate)) / (1000 * 60 * 60 * 24));
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'Planning':
        return 'bg-yellow-100 text-yellow-800';
      case 'Confirmed':
        return 'bg-green-100 text-green-800';
      case 'Completed':
        return 'bg-blue-100 text-blue-800';
      case 'Cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (!currentUser) {
    return (
      <div className="max-w-md mx-auto mt-20 p-6 bg-white rounded-lg shadow-md text-center">
        <i className="ri-user-line text-6xl text-gray-400 mb-4"></i>
        <h2 className="text-2xl font-bold text-gray-800 mb-2">Login Required</h2>
        <p className="text-gray-600 mb-4">Please login to view your AI travel plans</p>
        <button
          onClick={() => navigate('/login')}
          className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md"
        >
          Login Now
        </button>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="text-center">
          <i className="ri-loader-4-line text-4xl text-blue-600 animate-spin mb-4"></i>
          <p className="text-gray-600 text-lg">Loading your AI travel plans...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto p-4 md:p-6 font-shantell">
      {/* Header */}
      <div className="text-center mb-8">
        <h1 className="text-3xl md:text-4xl font-bold text-gray-800 mb-2">
          <i className="ri-robot-line text-blue-600 mr-2"></i>
          My AI Travel Plans
        </h1>
        <p className="text-gray-600 text-lg">
          Manage and view all your AI-generated travel plans
        </p>
      </div>

      {/* Action Bar */}
      <div className="flex justify-between items-center mb-8">
        <div className="text-gray-600">
          {travelPlans?.length || 0} travel plan{(travelPlans?.length || 0) !== 1 ? 's' : ''} found
        </div>
        <button
          onClick={() => navigate('/ai-travel-planner')}
          className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-6 py-3 rounded-md font-medium flex items-center"
        >
          <i className="ri-add-line mr-2"></i>
          Create New AI Plan
        </button>
      </div>

      {/* Travel Plans Grid */}
      {travelPlans && travelPlans.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {travelPlans.map((plan) => {
            const duration = calculateDuration(plan.startDate, plan.endDate);
            const totalCost = plan.recommendations?.reduce((sum, rec) => sum + (rec.estimatedCost || 0), 0) || 0;
            
            return (
              <div key={plan._id} className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
                {/* Card Header */}
                <div className="bg-gradient-to-r from-blue-500 to-purple-500 text-white p-4">
                  <div className="flex justify-between items-start mb-2">
                    <h3 className="text-xl font-bold truncate">{plan.planName}</h3>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(plan.status)}`}>
                      {plan.status}
                    </span>
                  </div>
                  <p className="text-blue-100 flex items-center">
                    <i className="ri-map-pin-line mr-1"></i>
                    {plan.destination}
                  </p>
                </div>

                {/* Card Body */}
                <div className="p-4">
                  {/* Trip Details */}
                  <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
                    <div className="text-center bg-gray-50 p-3 rounded-lg">
                      <i className="ri-calendar-line text-blue-600 text-lg mb-1 block"></i>
                      <div className="font-medium text-gray-700">Duration</div>
                      <div className="text-gray-600">{duration} days</div>
                    </div>
                    <div className="text-center bg-gray-50 p-3 rounded-lg">
                      <i className="ri-money-dollar-circle-line text-green-600 text-lg mb-1 block"></i>
                      <div className="font-medium text-gray-700">Budget</div>
                      <div className="text-gray-600">{formatCurrency(plan.budget)}</div>
                    </div>
                  </div>

                  {/* Travel Dates */}
                  <div className="mb-4">
                    <div className="text-sm text-gray-500 mb-1">Travel Dates</div>
                    <div className="text-gray-700 font-medium">
                      {formatDate(plan.startDate)} - {formatDate(plan.endDate)}
                    </div>
                  </div>

                  {/* Travel Style & Group */}
                  <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
                    <div>
                      <div className="text-gray-500 mb-1">Travel Style</div>
                      <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium capitalize">
                        {plan.travelStyle}
                      </span>
                    </div>
                    <div>
                      <div className="text-gray-500 mb-1">Group Size</div>
                      <div className="text-gray-700 font-medium">
                        {plan.groupSize} {plan.groupSize === 1 ? 'person' : 'people'}
                      </div>
                    </div>
                  </div>

                  {/* Recommendations Count */}
                  {plan.recommendations && plan.recommendations.length > 0 && (
                    <div className="mb-4">
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-gray-500">AI Recommendations</span>
                        <span className="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium">
                          {plan.recommendations.length} suggestions
                        </span>
                      </div>
                      <div className="text-xs text-gray-500 mt-1">
                        Estimated cost: {formatCurrency(totalCost)}
                      </div>
                    </div>
                  )}

                  {/* Activities */}
                  {plan.activities && plan.activities.length > 0 && (
                    <div className="mb-4">
                      <div className="text-sm text-gray-500 mb-2">Preferred Activities</div>
                      <div className="flex flex-wrap gap-1">
                        {plan.activities.slice(0, 3).map((activity, index) => (
                          <span key={index} className="bg-purple-100 text-purple-800 px-2 py-1 rounded-full text-xs capitalize">
                            {activity}
                          </span>
                        ))}
                        {plan.activities.length > 3 && (
                          <span className="bg-gray-100 text-gray-600 px-2 py-1 rounded-full text-xs">
                            +{plan.activities.length - 3} more
                          </span>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Created Date */}
                  <div className="text-xs text-gray-400 mb-4">
                    Created on {formatDate(plan.createdAt)}
                  </div>
                </div>

                {/* Card Footer */}
                <div className="px-4 pb-4">
                  <button
                    onClick={() => navigate(`/ai-travel-plan/${plan._id}`)}
                    className="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 px-4 rounded-md font-medium transition-colors flex items-center justify-center"
                  >
                    <i className="ri-eye-line mr-2"></i>
                    View Details
                  </button>
                </div>
              </div>
            );
          })}
        </div>
      ) : (
        /* Empty State */
        <div className="text-center py-16">
          <i className="ri-robot-line text-8xl text-gray-300 mb-6"></i>
          <h3 className="text-2xl font-bold text-gray-600 mb-4">No AI Travel Plans Yet</h3>
          <p className="text-gray-500 mb-8 max-w-md mx-auto">
            You haven't created any AI travel plans yet. Start planning your next adventure with our intelligent travel assistant!
          </p>
          <button
            onClick={() => navigate('/ai-travel-planner')}
            className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-4 rounded-md font-medium text-lg flex items-center mx-auto"
          >
            <i className="ri-robot-line mr-2"></i>
            Create Your First AI Travel Plan
          </button>
        </div>
      )}
    </div>
  );
};

export default AITravelPlansList;
